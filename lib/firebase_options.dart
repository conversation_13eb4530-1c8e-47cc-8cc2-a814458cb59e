// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyA2X0tFB28LBWHiZjePovyoLqzt5X0u4wA',
    appId: '1:365005048275:web:6fe5aeb5631ad2ee460726',
    messagingSenderId: '365005048275',
    projectId: 'aslaaios',
    authDomain: 'aslaaios.firebaseapp.com',
    storageBucket: 'aslaaios.appspot.com',
    measurementId: 'G-KMTH250C0P',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCHhldCAYU8vbLgSa5eLBSw4-z5-3_gjpw',
    appId: '1:365005048275:android:86d7b0a73301a24e460726',
    messagingSenderId: '365005048275',
    projectId: 'aslaaios',
    storageBucket: 'aslaaios.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyClEt19XVBaH9DaHM4AyEn_fjwkWB9MIL0',
    appId: '1:365005048275:ios:34c4fd2c64e61960460726',
    messagingSenderId: '365005048275',
    projectId: 'aslaaios',
    storageBucket: 'aslaaios.appspot.com',
    iosClientId: '365005048275-q1tg6nctccj9bpsm8mdntc7rhdjjqrng.apps.googleusercontent.com',
    iosBundleId: 'com.elec.aslaa',
  );
}
