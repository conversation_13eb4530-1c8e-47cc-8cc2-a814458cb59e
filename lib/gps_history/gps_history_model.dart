import 'package:aslaa/flutter_flow/flutter_flow_model.dart';

import '../flutter_flow/flutter_flow_google_map.dart';
import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';

class GpsHistoryModel extends FlutterFlowModel {
  ///  State fields for stateful widgets in this page.

  // State field(s) for DropDown widget.
  String? dropDownValue;
  // State field(s) for GoogleMap widget.
  LatLng? googleMapsCenter;
  final googleMapsController = Completer<GoogleMapController>();

  /// Initialization and disposal methods.

  void initState(BuildContext context) {}

  void dispose() {}

  /// Additional helper methods are added here.
}
