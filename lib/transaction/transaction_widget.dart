import 'package:aslaa/flutter_flow/flutter_flow_icon_button.dart';
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/flutter_flow/flutter_flow_util.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class TransactionWidget extends StatefulWidget {
  const TransactionWidget({Key? key}) : super(key: key);
  _TransactionWidgetState createState() => _TransactionWidgetState();
}

class _TransactionWidgetState extends State<TransactionWidget> {
  final scaffoldKey = GlobalKey<ScaffoldState>();
  List<DataRow>? filteredList;
  @override
  void initState() {
    super.initState();
    AppProvider provider = Provider.of<AppProvider>(context, listen: false);
    debugPrint('${provider.user!.wallet.transactions!}');
    // if (mounted)
    //   Future.delayed(Duration.zero, () async {
    //     SharedPreferences _prefs = await SharedPreferences.getInstance();
    //     String? token = _prefs.getString('token');
    //     if (token != null) {
    //       AppProvider provider =
    //           Provider.of<AppProvider>(context, listen: false);
    //       try {
    //         provider.reload();
    //         debugPrint('${provider.user!.wallet.transactions}');
    //         setState(() {
    //           if (provider.user!.wallet.transactions != null) {

    //             List<dynamic> transactions =
    //                 provider.user!.wallet.transactions!;

    //           }
    //         });
    //       } catch (err) {
    //         // if (err is HttpResultException) {
    //         //   final HttpResultException _err = err;
    //         //   final ContentType contentType = _err.statusCode == 200
    //         //       ? ContentType.warning
    //         //       : ContentType.failure;
    //         //   showAnimatedSnackbar(
    //         //       context, _err.message, _err.title, contentType);
    //         // }
    //         debugPrint('$err');
    //         debugPrint('token expired or network error');
    //       }
    //     }
    //   });
  }

  Widget build(BuildContext context) {
    AppProvider provider = Provider.of<AppProvider>(context, listen: false);
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 48, 24, 12),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    FFLocalizations.of(context).getText(
                      'mns9z8mb' /* Transactions */,
                    ),
                    style: FlutterFlowTheme.of(context).bodyText1.override(
                          fontFamily: 'Roboto',
                          fontSize: 24,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                  FlutterFlowIconButton(
                    borderColor: Colors.transparent,
                    borderRadius: 24,
                    borderWidth: 1,
                    buttonSize: 48,
                    icon: Icon(
                      Icons.home,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 24,
                    ),
                    onPressed: () {
                      context.pushNamed('dashboard');
                      // print('IconButton pressed ...');
                    },
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24, 0, 24, 0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: Container(
                      width: 100,
                      height: MediaQuery.of(context).size.height * 0.8,
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                      ),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Container(
                          width: 700,
                          child: PaginatedDataTable2(
                            columns: [
                              DataColumn2(
                                label: DefaultTextStyle.merge(
                                  softWrap: true,
                                  child: Text(
                                    FFLocalizations.of(context).getText(
                                      'mns9z8mc' /* Date */,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .title3
                                        .override(
                                          fontFamily: 'Roboto',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                          fontSize: 12,
                                        ),
                                  ),
                                ),
                              ),
                              DataColumn2(
                                label: DefaultTextStyle.merge(
                                  softWrap: true,
                                  child: Text(
                                    FFLocalizations.of(context).getText(
                                      'mns9z8md' /* Mode */,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .title3
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontSize: 12,
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                        ),
                                  ),
                                ),
                              ),
                              DataColumn2(
                                label: DefaultTextStyle.merge(
                                  softWrap: true,
                                  child: Text(
                                    FFLocalizations.of(context).getText(
                                      'mns9z8me' /* Edit Header 3 */,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .title3
                                        .override(
                                          fontFamily: 'Roboto',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                          fontSize: 12,
                                        ),
                                  ),
                                ),
                              ),
                              DataColumn2(
                                label: DefaultTextStyle.merge(
                                  softWrap: true,
                                  child: Text(
                                    FFLocalizations.of(context).getText(
                                      'mns9z8mf' /* Edit Header 4 */,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .title3
                                        .override(
                                            fontFamily: 'Roboto',
                                            color: FlutterFlowTheme.of(context)
                                                .primaryText,
                                            fontSize: 12),
                                  ),
                                ),
                              ),
                            ],
                            source: TransactionSource(
                                data: provider.user!.wallet.transactions!),
                            headingRowColor: WidgetStateProperty.all(
                              FlutterFlowTheme.of(context).primaryBackground,
                            ),
                            headingRowHeight: 56,
                            dataRowHeight: 56,
                            border: TableBorder(
                              borderRadius: BorderRadius.circular(0),
                            ),
                            dividerThickness: 1,
                            minWidth: 49,
                            columnSpacing: 100,
                            horizontalMargin: 10,
                            rowsPerPage: 8,
                            showCheckboxColumn: false,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// The "soruce" of the table
class TransactionSource extends DataTableSource {
  // Generate some made-up data
  List<dynamic> data;

  TransactionSource({required this.data});

  @override
  bool get isRowCountApproximate => false;
  @override
  int get rowCount => data.length;
  @override
  int get selectedRowCount => 0;
  @override
  DataRow getRow(int index) {
    return DataRow(cells: [
      DataCell(Text((data[index]['ts'] ?? '').toString())),
      DataCell(Text('${getFormatedNumber(data[index]["amount"])}')),
      DataCell(Text(data[index]['mode'] ?? '')),
      DataCell(Text(data[index]['description'] ?? '')),
    ]);
  }
}
