import 'dart:convert';
import 'dart:typed_data';

class Util {
  //判断空
  static bool isEmpty(var val) {
    return (val == null || val == '' || val == 'null') ? true : false;
  }

  // 是否不是空字符串
  static bool isNotEmpty(String? str) {
    return !isEmpty(str) ? true : false;
  }

  static bool isEmptyList(List list) {
    return (list == [] || list.length == 0) ? true : false;
  }

  static bool isNotEmptyList(List list) {
    return !isEmptyList(list) ? true : false;
  }

  //处理空值
  static dynamic fuckEmpty(var val) {
    return val != null ? json.encode(val) : null;
  }

  static safeStr(dynamic str, [dynamic safeValue = ""]) {
    return isEmpty(str) ? safeValue : str;
  }

  static hex(int c) {
    if (c >= '0'.codeUnitAt(0) && c <= '9'.codeUnitAt(0)) {
      return c - '0'.codeUnitAt(0);
    }
    if (c >= 'A'.codeUnitAt(0) && c <= 'F'.codeUnitAt(0)) {
      return (c - 'A'.codeUnitAt(0)) + 10;
    }
  }

  static toUnitList(String str) {
    int length = str.length;
    if (length % 2 != 0) {
      str = "0" + str;
      length++;
    }
    List<int> s = str.toUpperCase().codeUnits;
    Uint8List bArr = Uint8List(length >> 1);
    for (int i = 0; i < length; i += 2) {
      bArr[i >> 1] = ((hex(s[i]) << 4) | hex(s[i + 1]));
    }

    print("bArr ${bArr}");
    return bArr;
  }
}
