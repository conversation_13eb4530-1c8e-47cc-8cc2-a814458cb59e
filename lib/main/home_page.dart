import 'package:aslaa/main/util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/device.dart';
import '../models/user.dart';
import '../providers/app_provider.dart';
import '../flutter_flow/internationalization.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key, required this.title}) : super(key: key);

  final String title;

  @override
  State<HomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<HomePage> {
  List<Device>? devices;
  bool isLoading = false;

// 通用白色背景阴影
  final kBoxShadowStyle = <BoxShadow>[
    const BoxShadow(
        color: Color.fromRGBO(12, 0, 51, 0.05),
        offset: Offset(0, 4),
        blurRadius: 8)
  ];

  @override
  void initState() {
    super.initState();
    _loadDevices();
  }

  void _loadDevices() {
    setState(() {
      isLoading = true;
    });

    AppProvider appProvider = Provider.of<AppProvider>(context, listen: false);
    User? user = appProvider.authClient;

    setState(() {
      devices = user?.devices ?? [];
      isLoading = false;
    });
  }

  renderRow(i) {
    Device device = devices![i];
    AppProvider appProvider = Provider.of<AppProvider>(context, listen: false);
    DeviceStatus deviceStatus = appProvider.ds;

    return Container(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 20, bottom: 15),
      margin: const EdgeInsets.only(left: 15, right: 15, top: 15),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          boxShadow: kBoxShadowStyle),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                device.deviceName.isNotEmpty
                    ? device.deviceName
                    : "Unknown Device",
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 10),
              if (device.isDefault == true)
                const Icon(Icons.star, color: Colors.amber),
              const Spacer(),
              Container(
                padding: const EdgeInsets.only(
                    top: 7, bottom: 7, left: 15, right: 15),
                decoration: BoxDecoration(
                    color: deviceStatus.isOnline == true
                        ? Colors.green
                        : Colors.grey,
                    borderRadius: const BorderRadius.all(Radius.circular(5))),
                child: Text(
                  deviceStatus.isOnline == true ? "Online" : "Offline",
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              )
            ],
          ),
          const SizedBox(height: 5),
          Text(
            device.deviceNumber.isNotEmpty ? device.deviceNumber : "N/A",
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 15),

          // Speed Display Section
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.speed, color: Colors.blue, size: 24),
                const SizedBox(width: 10),
                Column(
                  children: [
                    Text(
                      FFLocalizations.of(context).getText('speed'),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    TweenAnimationBuilder<double>(
                      tween: Tween<double>(
                        begin: (deviceStatus.Speed.toDouble()) - 2,
                        end: deviceStatus.Speed.toDouble(),
                      ),
                      duration: const Duration(seconds: 2),
                      builder: (context, value, child) {
                        return Text(
                          '${value.toStringAsFixed(1)} ${FFLocalizations.of(context).getText('kmh')}',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                            fontFamily: 'PorscheNumber',
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 15),

          // Device Status Indicators
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatusIndicator(
                icon: Icons.gps_fixed,
                label: "GPS",
                isActive: deviceStatus.gps == true,
              ),
              _buildStatusIndicator(
                icon: Icons.battery_full,
                label: "Battery",
                isActive: deviceStatus.volt > 12.0,
                value: "${deviceStatus.volt.toStringAsFixed(1)}V",
              ),
              _buildStatusIndicator(
                icon: Icons.thermostat,
                label: "Temp",
                isActive: true,
                value: "${deviceStatus.temp.toStringAsFixed(0)}°C",
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator({
    required IconData icon,
    required String label,
    required bool isActive,
    String? value,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: isActive ? Colors.green : Colors.grey,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: isActive ? Colors.green : Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
        if (value != null) ...[
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 9,
              color: Colors.grey,
            ),
          ),
        ],
      ],
    );
  }

  Widget setupNoDataView() {
    return Center(
      child: Container(
          height: 300,
          padding: const EdgeInsets.all(20),
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Container(
                  width: 180,
                  height: 180,
                  child: Image.asset(
                    "assets/images/driver-license.png",
                    fit: BoxFit.fill,
                  )),
              const SizedBox(
                height: 20,
              ),
              const Text("No devices found...",
                  style: TextStyle(color: Color(0xFF666666), fontSize: 18)),
            ],
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDevices,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Util.isEmpty(devices)
              ? setupNoDataView()
              : Container(
                  color: const Color(0xFFFAFAFA),
                  child: ListView.builder(
                    padding: const EdgeInsets.only(bottom: 10),
                    itemCount: devices?.length ?? 0,
                    itemBuilder: (context, i) => renderRow(i),
                  ),
                ),
      bottomNavigationBar: SizedBox(
          height: 70 + MediaQuery.of(context).padding.bottom,
          child: BottomAppBar(
            elevation: 15,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                GestureDetector(
                  onTap: () {
                    _loadDevices();
                  },
                  child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    height: 40,
                    alignment: Alignment.center,
                    decoration: const BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.all(Radius.circular(5.0))),
                    child: const Text(
                      "Refresh Devices",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                )
              ],
            ),
          )),
    );
  }
}
