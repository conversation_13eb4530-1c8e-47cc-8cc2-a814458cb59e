import 'dart:math';

import 'package:aslaa/main/util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter/services.dart';

import '../manager/ble_manager.dart';

class DeviceInterActionPage extends StatefulWidget {
  DeviceInterActionPage(
      {Key? key, required this.result, required this.characteristic})
      : super(key: key);
  final ScanResult result;
  BluetoothCharacteristic characteristic;
  @override
  DeviceInterActionPageState createState() => DeviceInterActionPageState();
}

class DeviceInterActionPageState extends State<DeviceInterActionPage> {
  final GlobalKey _formKey = GlobalKey<FormState>();
  List<int> _getRandomBytes() {
    final math = Random();
    return [
      math.nextInt(255),
      math.nextInt(255),
      math.nextInt(255),
      math.nextInt(255)
    ];
  }

  bool _isObscure = true;
  Color _eyeColor = Colors.grey;

  String checkTitle1 = "Write with response(write request)";
  String checkTitle2 = "Write without response(write command)";
  String checkTitle = "";

  final asciiController = TextEditingController();
  final hexController = TextEditingController();
  final decimalController = TextEditingController();

  @override
  void dispose() {
    decimalController.dispose();
    asciiController.dispose();
    hexController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    hexController.addListener(() {
      Uint8List int2utf8 = Util.toUnitList(hexController.text);

      String intStr = int2utf8.toString();
      decimalController.text =
          intStr.substring(1, intStr.length - 1).replaceAll(",", " ");
      asciiController.text = hexController.text.isNotEmpty ? "********" : "";
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: setupTitle(),
      insetPadding: const EdgeInsets.all(20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      titlePadding: const EdgeInsets.only(left: 24, right: 10, top: 10),
      content: SizedBox(
        height: 360,
        width: MediaQuery.of(context).size.width - 40,
        child: Form(
          key: _formKey, // 设置globalKey，用于后面获取FormStat
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: ListView(
            // padding: const EdgeInsets.symmetric(horizontal: 20),
            children: [
              setupHexTextField(), // account
              const SizedBox(height: 10),
              setupASCIITextField(context), // password
              const SizedBox(height: 10),
              setupDecimalTextField(),
              const SizedBox(height: 30),
              setupRadio(checkTitle1),
              setupRadio(checkTitle2),
              const SizedBox(height: 20),
              setupButton(context), // password button
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }

  Row setupRadio(String title) {
    return Row(
      children: [
        Radio<String>(
            visualDensity: const VisualDensity(
                horizontal: VisualDensity.minimumDensity,
                vertical: VisualDensity.minimumDensity),
            value: title,
            // activeColor: Colors.red,
            fillColor: WidgetStateProperty.resolveWith((state) {
              return Colors.blue;
            }),
            focusColor: Colors.orange,
            groupValue: checkTitle,
            toggleable: false,
            onChanged: (value) {
              setState(() {
                checkTitle = value!;
              });
            }),
        Text(
          title,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        )
      ],
    );
  }

  Widget setupButton(BuildContext context) {
    return Row(
      children: [
        Spacer(),
        GestureDetector(
          onTap: () {
            hexController.text = "";
            decimalController.text = "";
            asciiController.text = "";
          },
          child: const Text(
            "Clear",
            style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w500),
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: () {
            Uint8List int2utf8 = Util.toUnitList(hexController.text);
            BleManager.instance.characteristicToWriteValue(
                widget.characteristic, int2utf8,
                withoutResponse: checkTitle != checkTitle1);
            Navigator.pop(context, true);
            print("Characteristic write successful !!");
          },
          child: Container(
            height: 35,
            width: 100,
            alignment: Alignment.center,
            decoration: const BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            child: Text('Send',
                style: Theme.of(context).primaryTextTheme.bodyLarge),
          ),
        ),
        const Spacer()
      ],
    );
  }

  Widget setupDecimalTextField() {
    return TextFormField(
      controller: decimalController,
      decoration: InputDecoration(
          labelText: 'Decimal',
          suffixIcon: IconButton(
            icon: Icon(
              Icons.copy_all,
              color: _eyeColor,
            ),
            onPressed: () {
              //复制
              Clipboard.setData(ClipboardData(text: decimalController.text));
              print("Copy successful !!");
            },
          )),
      validator: (v) {
        if (v!.isEmpty) {
          return 'please enter Decimal';
        }
        return null;
      },
    );
  }

  Widget setupASCIITextField(BuildContext context) {
    return TextFormField(
        controller: asciiController,
        obscureText: _isObscure, // 是否显示文字
        validator: (v) {
          if (v!.isEmpty) {
            return 'please enter ASCII';
          }
          return null;
        },
        decoration: InputDecoration(
            labelText: "ASCII",
            suffixIcon: IconButton(
              icon: Icon(
                Icons.copy_all,
                color: _eyeColor,
              ),
              onPressed: () {
                Clipboard.setData(ClipboardData(text: hexController.text));
                print("Copy successful !!");
              },
            )));
  }

  Widget setupHexTextField() {
    return TextFormField(
      controller: hexController,
      validator: (v) {
        if (v!.isEmpty) {
          return 'please enter Hex';
        }
        return null;
      },
      decoration: InputDecoration(
          labelText: 'Hex',
          suffixIcon: IconButton(
            icon: Icon(
              Icons.copy_all,
              color: _eyeColor,
            ),
            onPressed: () {
              Clipboard.setData(ClipboardData(text: hexController.text));
              print("Copy successful !!");
            },
          )),
    );
  }

  Widget buildTitleLine() {
    return Padding(
        padding: const EdgeInsets.only(left: 12.0, top: 4.0),
        child: Align(
          alignment: Alignment.bottomLeft,
          child: Container(
            color: Colors.black,
            width: 40,
            height: 2,
          ),
        ));
  }

  Widget setupTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Spacer(),
            GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  Navigator.pop(context, true);
                },
                child: const Icon(
                  Icons.close,
                ))
          ],
        ),
        Text(
          'Service',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        const SizedBox(
          height: 5,
        ),
        const Text('Characteristic'),
        const SizedBox(
          height: 5,
        ),
        Text('${widget.characteristic.uuid.toString()}',
            style: Theme.of(context)
                .textTheme
                .bodyLarge
                ?.copyWith(color: Theme.of(context).textTheme.bodySmall?.color))
      ],
    );
  }
}
