import 'dart:async';
import 'dart:math';

import 'package:aslaa/main/util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../manager/ble_manager.dart';
import 'device_interaction_page.dart';

class DeviceDetailsPage extends StatefulWidget {
  DeviceDetailsPage({Key? key, required this.result}) : super(key: key);
  final ScanResult result;

  @override
  State<DeviceDetailsPage> createState() => _DeviceDetailsPageState();
}

class _DeviceDetailsPageState extends State<DeviceDetailsPage> {
  late BluetoothDevice device;

  @override
  void initState() {
    // TODO: implement initState
    device = widget.result.device;

    Future.delayed(const Duration(seconds: 1), () {
      setupDeviceConnectedToDiscoverServices();
    });
    super.initState();
  }

  // 连接设备发现服务
  setupDeviceConnectedToDiscoverServices() async {
    /// 连接设备
    await BleManager.instance.connectDevice(device).then((e) => () async {
          print("连接设备 -- ");

          List<BluetoothService> services =
              await BleManager.instance.deviceToDiscoverServices(device);
          print("发现服务 ${services}");
        });
    await BleManager.instance.deviceToDiscoverServices(device);
  }

// 通用白色背景阴影
  final kBoxShadowStyle = <BoxShadow>[
    const BoxShadow(
        color: Color.fromRGBO(12, 0, 51, 0.05),
        offset: Offset(0, 4),
        blurRadius: 8)
  ];

  List<int> _getRandomBytes() {
    final math = Random();
    return [
      math.nextInt(255),
      math.nextInt(255),
      math.nextInt(255),
      math.nextInt(255)
    ];
  }

  List<Widget> _buildServiceTiles(List<BluetoothService> services) {
    print("services $services");
    return services
        .map(
          (s) => Container(
            margin: const EdgeInsets.only(left: 15, right: 15, bottom: 10),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                boxShadow: kBoxShadowStyle),
            child: ServiceTile(
              service: s,
              characteristicTiles: s.characteristics
                  .map(
                    (c) => CharacteristicTile(
                      characteristic: c,
                      onReadPressed: () => c.read(),
                      onWritePressed: () async {
                        showDialog(
                          context: context,
                          builder: (ctx) {
                            return DeviceInterActionPage(
                                result: widget.result, characteristic: c);
                          },
                        );
                        // await c.write(_getRandomBytes(), withoutResponse: true);
                        // await c.read();
                      },
                      onNotificationPressed: () async {
                        // await c.setNotifyValue(!c.isNotifying);
                        BleManager.instance
                            .characteristicToSetNotifyValue(c, !c.isNotifying);
                        await c.read();
                      },
                      descriptorTiles: c.descriptors
                          .map(
                            (d) => DescriptorTile(
                              descriptor: d,
                              onReadPressed: () => d.read(),
                              onWritePressed: () => d.write(_getRandomBytes()),
                            ),
                          )
                          .toList(),
                    ),
                  )
                  .toList(),
            ),
          ),
        )
        .toList();
  }

  setupMoreInfoWidget(ScanResult scanResult) {
    return Column(
      children: [
        if (!Util.isEmpty(scanResult.advertisementData.txPowerLevel))
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "TX Power Level",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text("${scanResult.advertisementData.txPowerLevel}"),
            ],
          )),
        if (!Util.isEmpty(scanResult.advertisementData.localName))
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "Complete Local Name",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(scanResult.advertisementData.localName),
            ],
          )),
        if (!Util.isEmpty(scanResult.advertisementData.manufacturerData) &&
            scanResult.advertisementData.manufacturerData.isNotEmpty)
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "Manufacturer Specific Data",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(BleManager.instance.getNiceManufacturerData(
                  scanResult.advertisementData.manufacturerData)),
            ],
          )),
        if (!Util.isEmpty(scanResult.advertisementData.serviceUuids) &&
            scanResult.advertisementData.serviceUuids.isNotEmpty)
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "Service UUIDs",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(scanResult.advertisementData.serviceUuids
                  .join(', ')
                  .toUpperCase()),
            ],
          )),
        if (!Util.isEmpty(scanResult.advertisementData.serviceData) &&
            scanResult.advertisementData.serviceData.isNotEmpty)
          (Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              const Divider(),
              const SizedBox(
                height: 5,
              ),
              const Text(
                "Service Data",
                style: TextStyle(color: Color(0xFF999999)),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(BleManager.instance.getNiceServiceData(scanResult
                  .advertisementData.serviceData
                  .cast<Guid, List<int>>())),
            ],
          ))
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(device.name),
        actions: <Widget>[
          StreamBuilder<BluetoothConnectionState>(
            stream: device.connectionState,
            initialData: BluetoothConnectionState.connecting,
            builder: (c, snapshot) {
              VoidCallback? onPressed;
              String text;
              switch (snapshot.data) {
                case BluetoothConnectionState.connected:
                  onPressed =
                      () => BleManager.instance.disconnectDevice(device);
                  text = 'DISCONNECT';
                  break;
                case BluetoothConnectionState.disconnected:
                  onPressed = () => setupDeviceConnectedToDiscoverServices();
                  text = 'CONNECT';
                  break;
                default:
                  onPressed = null;
                  text = snapshot.data.toString().substring(21).toUpperCase();
                  break;
              }
              return TextButton(
                onPressed: onPressed,
                child: Text(
                  text,
                  style: Theme.of(context)
                      .primaryTextTheme
                      .labelLarge
                      ?.copyWith(color: Colors.white),
                ),
              );
            },
          )
        ],
      ),
      body: Column(
        children: <Widget>[
          // 设备基本信息
          StreamBuilder<BluetoothConnectionState>(
            stream: device.connectionState,
            initialData: BluetoothConnectionState.connecting,
            builder: (c, snapshot) => Card(
                elevation: 2,
                margin: const EdgeInsets.only(
                    left: 10, right: 10, top: 10, bottom: 10),
                child: Container(
                  padding: const EdgeInsets.only(
                      left: 10, right: 10, top: 5, bottom: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          (snapshot.data == BluetoothConnectionState.connected)
                              ? const Icon(
                                  Icons.bluetooth_connected,
                                  color: Colors.blue,
                                )
                              : const Icon(Icons.bluetooth_disabled),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                              'Device is ${snapshot.data.toString().split('.')[1]}.'),
                          const Spacer(),
                          StreamBuilder<bool>(
                            stream: device.isDiscoveringServices,
                            initialData: false,
                            builder: (c, snapshot) => IndexedStack(
                              index: snapshot.data! ? 1 : 0,
                              children: <Widget>[
                                IconButton(
                                  icon: const Icon(Icons.refresh),
                                  onPressed: () => BleManager.instance
                                      .deviceToDiscoverServices(device),
                                ),
                                const IconButton(
                                  icon: SizedBox(
                                    width: 18.0,
                                    height: 18.0,
                                    child: CircularProgressIndicator(
                                      valueColor:
                                          AlwaysStoppedAnimation(Colors.grey),
                                    ),
                                  ),
                                  onPressed: null,
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Text('${device.id}'),
                      setupMoreInfoWidget(widget.result)
                    ],
                  ),
                )),
          ),
          Expanded(
              child: SingleChildScrollView(
            child: StreamBuilder<List<BluetoothService>>(
              stream: device.services,
              initialData: const [],
              builder: (c, snapshot) {
                return Column(
                  children: _buildServiceTiles(snapshot.data!),
                );
              },
            ),
          )),
        ],
      ),
    );
  }
}

class ServiceTile extends StatelessWidget {
  final BluetoothService service;
  final List<CharacteristicTile> characteristicTiles;

  const ServiceTile(
      {Key? key, required this.service, required this.characteristicTiles})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (characteristicTiles.length > 0) {
      return Theme(
          data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
          child: ExpansionTile(
            title: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const Text('Service'),
                Text(
                  '0x${service.uuid.toString().toUpperCase().substring(4, 8)}',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).textTheme.bodySmall?.color),
                ),
              ],
            ),
            children: characteristicTiles,
          ));
    } else {
      return ListTile(
        title: const Text('Service'),
        subtitle:
            Text('0x${service.uuid.toString().toUpperCase().substring(4, 8)}'),
      );
    }
  }
}

class CharacteristicTile extends StatelessWidget {
  final BluetoothCharacteristic characteristic;
  final List<DescriptorTile> descriptorTiles;
  final VoidCallback? onReadPressed;
  final VoidCallback? onWritePressed;
  final VoidCallback? onNotificationPressed;

  const CharacteristicTile(
      {Key? key,
      required this.characteristic,
      required this.descriptorTiles,
      this.onReadPressed,
      this.onWritePressed,
      this.onNotificationPressed})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<int>>(
      stream: characteristic.value,
      initialData: characteristic.lastValue,
      builder: (c, snapshot) {
        final value = snapshot.data;
        return Column(
          children: [
            Divider(color: Colors.grey),
            ExpansionTile(
              title: ListTile(
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const Text('Characteristic'),
                    Text(
                        'UUID：0x${characteristic.uuid.toString().toUpperCase().substring(4, 8)}',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color:
                                Theme.of(context).textTheme.bodySmall?.color))
                  ],
                ),
                subtitle: Text(value.toString()),
                contentPadding: const EdgeInsets.all(0.0),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  IconButton(
                    icon: Icon(
                      Icons.file_download,
                      color:
                          Theme.of(context).iconTheme.color?.withOpacity(0.5),
                    ),
                    onPressed: onReadPressed,
                  ),
                  IconButton(
                    icon: Icon(Icons.file_upload,
                        color: Theme.of(context)
                            .iconTheme
                            .color
                            ?.withOpacity(0.5)),
                    onPressed: onWritePressed,
                  ),
                  IconButton(
                    icon: Icon(
                        characteristic.isNotifying
                            ? Icons.notifications_off
                            : Icons.notifications_on,
                        color: characteristic.isNotifying
                            ? Theme.of(context)
                                .iconTheme
                                .color
                                ?.withOpacity(0.5)
                            : Colors.blue),
                    onPressed: onNotificationPressed,
                  )
                ],
              ),
              children: descriptorTiles,
            )
          ],
        );
      },
    );
  }
}

class DescriptorTile extends StatelessWidget {
  final BluetoothDescriptor descriptor;
  final VoidCallback? onReadPressed;
  final VoidCallback? onWritePressed;

  const DescriptorTile(
      {Key? key,
      required this.descriptor,
      this.onReadPressed,
      this.onWritePressed})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const Text('Descriptor'),
          Text(
              'UUID：0x${descriptor.uuid.toString().toUpperCase().substring(4, 8)}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).textTheme.bodySmall?.color))
        ],
      ),
      subtitle: StreamBuilder<List<int>>(
        stream: descriptor.value,
        initialData: descriptor.lastValue,
        builder: (c, snapshot) => Text(snapshot.data.toString()),
      ),
      // trailing: Row(
      //   mainAxisSize: MainAxisSize.min,
      //   children: <Widget>[
      //     IconButton(
      //       icon: Icon(
      //         Icons.file_download,
      //         color: Theme.of(context).iconTheme.color?.withOpacity(0.5),
      //       ),
      //       onPressed: onReadPressed,
      //     ),
      //     IconButton(
      //       icon: Icon(
      //         Icons.file_upload,
      //         color: Theme.of(context).iconTheme.color?.withOpacity(0.5),
      //       ),
      //       onPressed: onWritePressed,
      //     )
      //   ],
      // ),
    );
  }
}

class AdapterStateTile extends StatelessWidget {
  const AdapterStateTile({Key? key, required this.state}) : super(key: key);

  final BluetoothState state;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.redAccent,
      child: ListTile(
        title: Text(
          'Bluetooth adapter is ${state.toString().substring(15)}',
          style: Theme.of(context).primaryTextTheme.titleMedium,
        ),
        trailing: Icon(
          Icons.error,
          color: Theme.of(context).primaryTextTheme.titleMedium?.color,
        ),
      ),
    );
  }
}
