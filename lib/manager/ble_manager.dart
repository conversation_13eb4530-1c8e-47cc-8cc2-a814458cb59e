// import 'package:flutter_blue/flutter_blue.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'dart:developer';
import 'dart:core';

typedef ScanResultHandler = void Function(List<ScanResult>? datas);
typedef CharacteristicHandler = void Function(List<int>? datas);

class BleManager {
  factory BleManager() => _getInstance();
  static BleManager get instance => _getInstance();
  static BleManager? _instance;
  List<ScanResult>? scanResults;

  BleManager._internal() {
    // if your terminal doesn't support color you'll see annoying logs like `\x1B[1;35m`
    FlutterBluePlus.setLogLevel(LogLevel.verbose, color: false);
  }
  static BleManager _getInstance() {
    return _instance ?? BleManager._internal();
  }

  startScan(ScanResultHandler dataHandler, {int timeout = 60}) {
    log("_scan");
    FlutterBluePlus.startScan(timeout: Duration(seconds: timeout));
    // _flutterBlue?.startScan(timeout: Duration(seconds: timeout),withServices: [Guid("0000fff0-0000-1000-XXXX-XXXXXXXXXXX")]);
    FlutterBluePlus.scanResults.listen(dataHandler);
  }

  stopScan() {
    log("_stopScan");
    FlutterBluePlus.stopScan();
  }

  Future<void> connectDevice(BluetoothDevice device) async {
    log("_connect");
    await device.connect(autoConnect: false);
  }

  Future<List<BluetoothService>> deviceToDiscoverServices(
      BluetoothDevice device) async {
    return await device.discoverServices();
  }

  Future<bool> characteristicToSetNotifyValue(
      BluetoothCharacteristic characteristic, bool notice) async {
    return await characteristic.setNotifyValue(notice);
  }

  listenCharacteristicValue(BluetoothCharacteristic characteristic,
      CharacteristicHandler characteristicHandler) {
    characteristic.value.listen(characteristicHandler);
  }

  Future<void> characteristicToWriteValue(
      BluetoothCharacteristic characteristic, List<int> list,
      {bool withoutResponse = false}) async {
    log(
      "_write  withoutResponse = $withoutResponse",
    );
    return await characteristic.write(list, withoutResponse: withoutResponse);
  }

  Future<List<int>> characteristicToReadValue(
      BluetoothCharacteristic characteristic) async {
    log("_read");
    return await characteristic.read();
  }

  List<BluetoothDevice> getConnectedDevices() {
    return FlutterBluePlus.connectedDevices;
  }

  Future<dynamic> disconnectDevice(BluetoothDevice device) async {
    log("_disconnect");
    return await device.disconnect();
  }

  String getNiceHexArray(List<int> bytes) {
    return '${bytes.map((i) => i.toRadixString(16).padLeft(2, '0')).join('')}'
        .toUpperCase();
  }

  String getNiceManufacturerData(Map<int, List<int>> data) {
    if (data.isEmpty) {
      return 'N/A';
    }
    List<String> res = [];
    data.forEach((id, bytes) {
      res.add(
          'Company Code：0x${id.toRadixString(16).toUpperCase().padLeft(4, "0")} \nData：0x${getNiceHexArray(bytes)}');
    });
    return res.join(', ');
  }

  /// 校验是否存在目标设备
  bool checkeDevice(String deviceNumber, Map<int, List<int>> data) {
    if (data.isEmpty) {
      return false;
    }

    var existDevice = false;
    for (var entry in data.entries) {
      dynamic value = entry.value;
      if (value.length > 12) {
        value = value.sublist(2, 10);
        deviceNumber = deviceNumber.padRight(16, '0');
        if (deviceNumber == getNiceHexArray(value)) {
          existDevice = true;
          break;
        }
      }
    }

    return existDevice;
  }

  String getNiceServiceData(Map<Guid, List<int>> data) {
    if (data.isEmpty) {
      return 'N/A';
    }
    List<String> res = [];
    data.forEach((id, bytes) {
      res.add('${id}: ${getNiceHexArray(bytes)}');
    });
    return res.join(', ');
  }
}
