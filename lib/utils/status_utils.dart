// lib/utils/status_utils.dart

import 'package:flutter/material.dart';

class StatusUtils {
  static Map<String, dynamic> getStatusProperties(String status) {
    TextStyle textStyle = TextStyle(fontFamily: 'PorscheNumber');
    switch (status) {
      case 'active':
        return {'color': Colors.green, 'text': 'Active', 'style': textStyle};
      case 'expired':
        return {'color': Colors.red, 'text': 'Expired', 'style': textStyle};
      case 'trial':
        return {'color': Colors.blue, 'text': 'Trial', 'style': textStyle};
      default:
        return {
          'color': Colors.black,
          'text': 'Status Unknown',
          'style': textStyle
        };
    }
  }
}
