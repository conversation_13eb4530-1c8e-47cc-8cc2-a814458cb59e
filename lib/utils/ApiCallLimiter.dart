import 'package:shared_preferences/shared_preferences.dart';

class ApiCallLimiter {
  static const String _callCountKey = 'api_call_count';
  static const String _lastResetKey = 'api_last_reset';

  // Maximum number of API calls allowed per user per day
  static const int maxCallsPerDay = 10;

  // Get the current date without time
  String getCurrentDateString() {
    final now = DateTime.now();
    return '${now.year}-${now.month}-${now.day}';
  }

  // Initialize the call count and reset date if not already done
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    if (!prefs.containsKey(_lastResetKey)) {
      await prefs.setString(_lastResetKey, getCurrentDateString());
      await prefs.setInt(_callCountKey, 0);
    }
  }

  // Check if the call limit has been reached
  Future<bool> canMakeApiCall() async {
    final prefs = await SharedPreferences.getInstance();
    final lastReset = prefs.getString(_lastResetKey) ?? getCurrentDateString();

    // If the date has changed, reset the count
    if (lastReset != getCurrentDateString()) {
      await prefs.setString(_lastResetKey, getCurrentDateString());
      await prefs.setInt(_callCountKey, 0);
      return true;
    }

    final callCount = prefs.getInt(_callCountKey) ?? 0;
    return callCount < maxCallsPerDay;
  }

  // Increment the API call count
  Future<void> incrementCallCount() async {
    final prefs = await SharedPreferences.getInstance();
    final callCount = (prefs.getInt(_callCountKey) ?? 0) + 1;
    await prefs.setInt(_callCountKey, callCount);
  }
}
