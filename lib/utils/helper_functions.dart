import 'package:aslaa/mqtt/mqtt_websocket.dart';
import 'package:aslaa/service/api_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:aslaa/models/user.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class HelperFunctions {
  // Displays a snackbar message for connection errors
  static void showConnectionErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'OK',
          onPressed: () {},
        ),
      ),
    );
  }

  // Notify the user that no response was received
  static void handleTimeout(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Time out'),
        duration: Duration(seconds: 3),
      ),
    );
  }

  static Future<bool> sendMqttCommand(
      MqttHandler? mqttHandler, User user, String command) async {
    if (mqttHandler == null) {
      print('MQTT Handler is not initialized.');
      return false;
    }
    try {
      return await mqttHandler.sendDeviceCommand(user, command);
    } catch (e) {
      print('Error sending MQTT command: $e');
      return false;
    }
  }

  // Sends a device command
  static Future<bool> sendDeviceCommand(
      MqttHandler? mqttHandler, User user, String command) async {
    if (command == 'as') {
      // Start the countdown animation
      // Assuming _countdownAnimationController is accessible here
      // _countdownAnimationController.reset();
      // _countdownAnimationController.forward();
      print('Countdown animation started');
    }

    if (mqttHandler != null && mqttHandler.isConnected) {
      try {
        // Attempt to send the command via MQTT
        bool mqttResult = await mqttHandler.sendDeviceCommand(user, command);
        return mqttResult;
      } catch (e) {
        print('MQTT_LOGS:: Error sending via MQTT: $e');
        return false; // Indicate failure if MQTT send fails
      }
    } else {
      print('MQTT_LOGS:: MQTT is not connected.');
      return false; // Indicate failure if MQTT is not connected
    }
  }

  // Fetches SIM status for a given device and updates the state if necessary
  static Future<Map<String, String>?> fetchSimStatus(
      ApiService apiService, String deviceNumber) async {
    try {
      final data = await apiService.fetchSimStatus(deviceNumber);
      if (data != null) {
        String balance = data['balance'];
        String rawDate = data['expiredDate'];
        return {'balance': balance, 'expiredDate': rawDate};
      } else {
        debugPrint("Failed to fetch SIM status or 'success' was false.");
      }
    } catch (e) {
      debugPrint("Error fetching SIM status: $e");
    }
    return null;
  }

  // Attempts to connect to the device and retries if necessary
  static Future<bool> attemptDeviceConnection(
      BuildContext context, User user) async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    bool isOnline = await appProvider.mqttHandler?.checkDeviceOnline() ?? false;
    int retryCount = 0;
    const int maxRetries = 3;

    while (!isOnline && retryCount < maxRetries) {
      debugPrint('Attempting to reconnect, try $retryCount');
      await Future.delayed(Duration(seconds: 2));
      isOnline = await appProvider.mqttHandler?.checkDeviceOnline() ?? false;
      retryCount++;
    }

    return isOnline;
  }

  // Loads user preference for Bluetooth functionality
  static Future<void> loadUserPreference(
      BuildContext context, Function(bool) onPreferenceLoaded) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isBluetoothFunctionalityEnabled =
        prefs.getBool('bluetoothScanningEnabled') ?? false;
    onPreferenceLoaded(isBluetoothFunctionalityEnabled);
  }

  // Saves user preference for Bluetooth functionality
  static Future<void> saveUserPreference(bool isEnabled) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('bluetoothScanningEnabled', isEnabled);
  }

  // Creates a car icon from an asset image
  static Future<BitmapDescriptor> createCarIcon() async {
    return await BitmapDescriptor.fromAssetImage(
      ImageConfiguration(devicePixelRatio: 2.5),
      'assets/images/car-image-icon.png',
    );
  }
}
