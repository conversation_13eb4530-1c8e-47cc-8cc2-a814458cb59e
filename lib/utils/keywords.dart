// lib/keywords.dart
const Set<String> unlockKeywords = {
  'машин нээ',
  'машин онгойлго',
  'машинд орьё',
  'машин нээгд',
  'нээ',
  'нэх',
  'нэг',
  'нэвт',
  'нэм',
  'нэр',
  'нэл',
  'нээнэ үү',
  'нээхүү',
  'онгойлго',
  'онгойлго нуу',
  'хаалга нээ',
  'хаалга онгойлго',
  'машины хаалга нээ',
  'машины хаалга онгойлго',
  'нээх',
  'онгойлгож',
  'машин нээнэ',
  'машин онгойлголоо',
  'машин онгойлгож байна',
  'нээлээ',
  'машин онгойлгоно',
  'машин онгойлгоод өгөөч',
  'машин онгойлгомоор байна',
  'машин онгойлгоод өг',
  'машины хаалга нээлээ',
  'машины хаалга онгойлголоо',
  'машины хаалга нээх',
  'машины хаалга онгойлгож байна',
  'машины хаалга онгойлгоно',
  'машины хаалга нээгдэж байна',
  'машины хаалга онгойлгоод өгөөч',
  'машины хаалга нээх үү',
  'машины хаалга нээмээр байна',
  'машины хаалга нээлгэмээр байна',
  'машины хаалга онгойлгоё',
  'машины хаалга нээхийг хүсэж байна',
  'машины хаалга нээхийг хүсэж байна уу',
  'машины хаалга онгойлгомоор байна',
  'машины хаалга нээлгэж өгөөч',
  'машины хаалга онгойлгохыг хүсэж байна',
  'машины хаалга нээмээр байна уу',
  'машины хаалга нээлгэж',
  'машины хаалга нээхийг хүсэж',
  'машины хаалга онгойлгож өгөөч',
  'машины хаалга нээгээч',
  'машины хаалга нээмээр',
  'машины хаалга нээхийг',
  'машины хаалгыг нээ',
  'машины хаалга нээлгэ',
  'машины хаалгыг нээмээр байна',
  'машины хаалга онгойлгомоор'
      'машин он',
  'машин онгой',
  'машин онгорхой',
  'машин онгой нуу',
  'машин нээлээ',
  'машин нээлт',
  'машин хаалга нээ',
  'машин хаалга нээлээ',
  'машин хаалга онгойлголоо',
  'машин хаалга нээх',
  'машин хаалга онгойлгож байна',
  'машин хаалга онгойлгоно',
  'машин хаалга нээгдэж байна',
  'машин хаалга онгойлгоод өгөөч',
  'машин хаалга нээх үү',
  'машин хаалга нээмээр байна',
  'машин хаалга нээлгэмээр байна',
  'машин хаалга онгойлгоё',
  'машин хаалга нээхийг хүсэж байна',
  'машин хаалга нээхийг хүсэж байна уу',
  'машин хаалга онгойлгомоор байна',
  'машин хаалга нээлгэж өгөөч',
  'машин хаалга онгойлгохыг хүсэж байна',
  'машин хаалга нээмээр байна уу',
  'машин хаалга нээлгэж',
  'машин хаалга нээхийг хүсэж',
  'машин хаалга онгойлгож өгөөч',
  'машин хаалга нээгээч',
  'машин хаалга нээмээр',
  'машин хаалга нээхийг',
  'машин хаалгыг нээ',
  'машин хаалга нээлгэ',
  'машин хаалгыг нээмээр байна',
  'машины хаалга онгойлгомоор'
};

const Set<String> lockKeywords = {
  'машин хаа',
  'машин түгж',
  'машинаас гарлаа',
  'хаа',
  'хар',
  'хат',
  'хачин'
      'хаалт',
  'чойжил',
  'хаалдаа',
  'хаагд',
  'хаая',
  'хаан уу',
  'хаан',
  'түгж',
  'түлхүүр',
  'түргэн',
  'түрүү',
  'түмэн',
  'түрээс',
  'түгжих',
  'түгжилт',
  'түүж',
  'түгжигч',
  'түгжээ',
  'түгж нүү',
  'түгжихүү',
  'машины хаалга хаа',
  'машины хаалга түгж',
  'машин түгжлээ',
  'машин хаалаа',
  'машин түгжиж байна',
  'машин хаагдлаа',
  'машин түгжиж өгөөч',
  'машин хааж өгөөч',
  'машины хаалга хаалаа',
  'машины хаалга түгжлээ',
  'машины хаалга түгжиж байна',
  'машины хаалга хаагдлаа',
  'машины хаалга хааж өгөөч',
  'машины хаалга түгжиж өгөөч',
  'машин түгжээд өгөөч',
  'машин хаагаад өгөөч',
  'машин түгжээд өг',
  'машин хаагаад өг',
  'машины хаалга түгжээд өгөөч',
  'машины хаалга хаагаад өгөөч',
  'машины хаалга түгжээд өг',
  'машины хаалга хаагаад өг',
  'машин түгжмээр байна',
  'машин хаамар байна',
  'машин түгжих үү',
  'машин хаах үү',
  'машины хаалга түгжмээр байна',
  'машины хаалга хаамар байна',
  'машины хаалга түгжих үү',
  'машины хаалга хаах үү',
  'машин түгжихийг хүсэж байна',
  'машин хаахыг хүсэж байна',
  'машины хаалга түгжихийг хүсэж байна',
  'машины хаалга хаахыг хүсэж байна',
  'машины хаалга түгжээд',
  'машины хаалга хаагаад',
  'машины хаалга түгжээдэх',
  'машины хаалга хаагаадах',
  'машин түгжээд ',
  'машин хаагаад',
  'машины хаалгыг түгжээд',
  'машины хаалга хаагаад байн',
  'машины хаалга хааж',
  'машины хаалга түгжиж',
  'машин түгжигд',
  'машин түгжээрэй',
  'машин түгж нүү',
  'машин түгжээч',
  'машин түү',
  'машин түүх',
};

const Set<String> startKeywords = {
  'машин ас',
  'машин асаа',
  'асаа',
  'машин асааж эхэл',
  'машинаа асаа',
  'машин асаж байна',
  'машин асаалаа',
  'машин аслаа',
  'машиныг асаа',
  'машиныг асаж байна',
  'машиныг асаалаа',
  'машиныг аслаа',
  'машинаа асааж эхэл',
  'машин асаагаад өгөөч',
  'машин асаж эхэллээ',
  'машин асаах үү',
  'машин асаамар байна',
  'машин асааж өгөөч',
  'машин асаагаад өг',
  'машиныг асааж өгөөч',
  'машиныг асаагаад өг',
  'машиныг асаамар байна',
  'машиныг асааж эхэл',
  'машинаа асааж өгөөч',
  'машинаа асаагаад өгөөч',
  'машинаа асааж байна',
  'машинаа асаамар байна',
  'машинаа асааж өг',
  'машинаа асаагаад өг',
  'машин аслаадаа',
  'машин асаагаад',
  'машин асааж өг',
  'машинаа асаагаадах',
  'машинаа асааж ',
  'машин асаалаа ',
  'машин асаж',
  'машин асаахыг хүсэж байна',
  'машин асаагаад байна',
  'машин асаал',
  'машин асаж эхэл',
  'машинаа асаамар',
  'машинаа асаахыг хүсэж байна',
  'машинаа асааж ас',
  'машинаа асаагаад',
  'машинаа аслаа',
  'ас',
  'us',
  'бас',
  'хас',
  'зас',
  'нас',
  'асуу',
  'асуудал',
  'асга',
  'хас нуу',
  'бас уу',
  'тас',
  'дас',
  'цаас',
  'баас',
  'ass',
  'buzz',
  "машиныг асаарай",
  "машиныг асаагаараи",
  "машинаа ас",
  "машинаа асаана",
  "машинаа асааж бай",
  "машин асах",
  "машин асааж үз",
  "машин асуулга",
  "машиныг асааж байгаа",
  "машиныг асааж бай",
  "машиныг асааж байна уу",
  "машиныг аслах",
  "машинаа аслах",
  "машинаа асааж байна уу",
  "машиныг асах үү",
  "машинаа асаагаараи",
  "машинаа асаагуур",
  "машиныг асаагаад өгнө үү",
  "машиныг асааж дуусга",
  "машинаа асааж дуусгарай",
  "машин аслахаар бэлтгэ",
  "машинаа асаагаач",
  "машинаа асааж турш",
  "машин аса",
  "машин асаж эхлэх",
  "машинаа асаж дуусгаарай",
  "машиныг асуугаарай",
  "машин асаагаад үз",
  "машин асаа нуу",
  "машин асаж бай"
};

const Set<String> untarKeywords = {
  "унтар",
  "уут",
  "ут",
  "унтар нуу",
  "унт нуу",
  "унтнуу",
  "унтахуу",
  "унтраах",
  "унтраагаараи",
  "унтрааж бай",
  "унтах",
  "унтахыг хүсэж байна",
  "унтаж байна",
  "унтаж дуусгаарай",
  "унтахыг хүсэж байна уу",
  "унтахаар бэлтгэ",
  "унтаарай",
  "унтаж бай",
  "унтаж байна уу",
  "унтлага",
  "унтаж өг",
  "унтах үү",
  "унтлагын бэлтгэл",
  "унтахаар орой",
  "унтахаас өмнө",
  "унтсан",
  "унтсанаа",
  "унтлаа",
  "унтаж байгаа",
  "унтахаар амрах",
  "унтах цаг",
  "унтаж амар",
  "унтах ор",
  "унтах газар",
  "унтаж ас",
  "унтах дуртай",
  "унтаж дуус",
  "машин унтраах",
  "машин унтраагаараи",
  "машин унтрааж бай",
  "машин унтах",
  "машин унтахыг хүсэж байна",
  "машин унтаж байна",
  "машин унтаж дуусгаарай",
  "машин унтахыг хүсэж байна уу",
  "машин унтахаар бэлтгэ",
  "машин унтаарай",
  "машин унтаж бай",
  "машин унтаж байна уу",
  "машин унтлага",
  "машин унтаж өг",
  "машин унтах үү",
  "машин унтлагын бэлтгэл",
  "машин унтахаар орой",
  "машин унтахаас өмнө",
  "машин унтсан",
  "машин унтсанаа",
  "машин унтлаа",
  "машин унтаж байгаа",
  "машин унтахаар амрах",
  "машин унтах цаг",
  "машин унтаж амар",
  "машин унтах ор",
  "машин унтах газар",
  "машин унтаж ас",
  "машин унтах дуртай",
  "машин унтаж дуус",
  "машин унтар",
  "машинд унтраах",
  "машинд унтраагаарай",
  "машинд унтрааж бай",
  "машинд унтах",
  "машинд унтахыг хүсэж байна",
  "машинд унтаж байна",
  "машинд унтаж дуусгаарай",
  "машинд унтахыг хүсэж байна уу",
  "машинд унтахаар бэлтгэ",
  "машинд унтаарай",
  "машинд унтаж бай",
  "машинд унтаж байна уу",
  "машинд унтлага",
  "машинд унтаж өг",
  "машинд унтах уу",
  "машинд унтлагын бэлтгэл",
  "машинд унтахаар орой",
  "машинд унтахаас өмнө",
  "машинд унтсан",
  "машинд унтсанаа",
  "машинд унтлаа",
  "машинд унтаж байгаа",
  "машинд унтахаар амрах",
  "машинд унтах цаг",
  "машинд унтаж амар",
  "машинд унтах ор",
  "машинд унтах газар",
  "машинд унтаж ас",
  "машинд унтах дуртай",
  "машинд унтаж дуус",
  "машинд унтар"
};
const Set<String> thankKeywords = {
  "баярлалаа",
  "баярлаж байна",
  "баярлалтай",
  "маш их баярлалаа",
  "таалагдлаа, баярлалаа",
  "баярлалаа, маш их",
  "баярласан байна",
  "баяр хүргэе",
  "танд баярлалаа",
  "таньд баярлалаа",
  "бид баярлалаа",
  "баярлалаа таньд",
  "баярлалаа та бүхэнд",
  "баярлалаа танай хамт олонд",
  "баярлалаа үү",
  "баярлалаа хэлэх гэж байна",
  "та бүхэнд баярлалаа",
  "баярлалаа хэлэхийг хүсч байна",
  "баярлалаа гэж хэлье",
  "баярлалаа гэдгийг илэрхийлье",
  "баярлалаа, бүрэн хангалуун",
  "баярлалаа, дуртай",
  "баярлалаа, дэмжсэнд",
  "баярлалаа, дэмжлэг үзүүлсэнд",
  "баярлалаа, тусалсанд",
  "баярлалаа, тус болсонд",
  "баярлалаа, хамт байсанд",
  "баярлалаа, надад тусалсанд",
  "баярлалаа, хүндэтгэсэнд",
  "баярлалаа, намайг дэмжсэнд"
};
