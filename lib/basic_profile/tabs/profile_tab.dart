import 'package:flutter/material.dart';
import '../../flutter_flow/flutter_flow_language_selector.dart';
import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';

class ProfileTab extends StatelessWidget {
  const ProfileTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      child: Form(
        autovalidateMode: AutovalidateMode.disabled,
        child: Padding(
          padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(24, 0, 0, 0),
                      child: Text(
                        FFLocalizations.of(context).getText(
                          'vripvise' /* Application Setting */,
                        ),
                        style: FlutterFlowTheme.of(context).bodyText1,
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        FFLocalizations.of(context).getText(
                          'ixqhqhqr' /* Language */,
                        ),
                        style: FlutterFlowTheme.of(context).bodyText1,
                      ),
                      FlutterFlowLanguageSelector(
                        width: 200,
                        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
                        borderColor: Colors.transparent,
                        dropdownIconColor: FlutterFlowTheme.of(context).secondaryText,
                        borderRadius: 8,
                        textStyle: TextStyle(
                          color: FlutterFlowTheme.of(context).primaryText,
                          fontWeight: FontWeight.normal,
                          fontSize: 13,
                        ),
                        hideFlags: true,
                        flagSize: 24,
                        flagTextGap: 8,
                        currentLanguage: FFLocalizations.of(context).languageCode,
                        languages: FFLocalizations.languages(),
                        onChanged: (lang) => setAppLanguage(context, lang),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        FFLocalizations.of(context).getText(
                          'ixqhqhqr' /* UI Theme */,
                        ),
                        style: FlutterFlowTheme.of(context).bodyText1,
                      ),
                      Container(
                        width: 200,
                        height: 50,
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).secondaryBackground,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            InkWell(
                              onTap: () async {
                                setDarkModeSetting(context, ThemeMode.light);
                              },
                              child: Container(
                                width: 80,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness == Brightness.light
                                      ? FlutterFlowTheme.of(context).secondaryColor
                                      : FlutterFlowTheme.of(context).secondaryBackground,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.wb_sunny,
                                      color: Theme.of(context).brightness == Brightness.light
                                          ? FlutterFlowTheme.of(context).primaryText
                                          : FlutterFlowTheme.of(context).secondaryText,
                                      size: 16,
                                    ),
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(4, 0, 0, 0),
                                      child: Text(
                                        FFLocalizations.of(context).getText(
                                          'qb1478yz' /* Light */,
                                        ),
                                        style: FlutterFlowTheme.of(context).bodyText1.override(
                                          fontFamily: 'Roboto',
                                          color: Theme.of(context).brightness == Brightness.light
                                              ? FlutterFlowTheme.of(context).primaryText
                                              : FlutterFlowTheme.of(context).secondaryText,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () async {
                                setDarkModeSetting(context, ThemeMode.dark);
                              },
                              child: Container(
                                width: 80,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? FlutterFlowTheme.of(context).secondaryColor
                                      : FlutterFlowTheme.of(context).secondaryBackground,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.nightlight_round,
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? FlutterFlowTheme.of(context).primaryText
                                          : FlutterFlowTheme.of(context).secondaryText,
                                      size: 16,
                                    ),
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(4, 0, 0, 0),
                                      child: Text(
                                        FFLocalizations.of(context).getText(
                                          'ixqhqhqr' /* Dark */,
                                        ),
                                        style: FlutterFlowTheme.of(context).bodyText1.override(
                                          fontFamily: 'Roboto',
                                          color: Theme.of(context).brightness == Brightness.dark
                                              ? FlutterFlowTheme.of(context).primaryText
                                              : FlutterFlowTheme.of(context).secondaryText,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
