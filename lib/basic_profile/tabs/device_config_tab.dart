import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../flutter_flow/flutter_flow_theme.dart';
import '../../flutter_flow/flutter_flow_util.dart';
import '../../providers/app_provider.dart';
import '../../models/user.dart';

class DeviceConfigTab extends StatefulWidget {
  const DeviceConfigTab({Key? key}) : super(key: key);

  @override
  _DeviceConfigTabState createState() => _DeviceConfigTabState();
}

class _DeviceConfigTabState extends State<DeviceConfigTab> {
  // Local state for optimistic updates
  Map<String, bool> _localToggleStates = {};

  // Persistent state for toggles when device doesn't support the field
  Map<String, bool> _persistentToggleStates = {};

  // Store recent device responses
  List<String> _deviceResponses = [];

  // Stream subscription for raw MQTT messages
  StreamSubscription<String>? _rawMessageSubscription;

  // Track last received message to avoid duplicates
  String? _lastReceivedMessage;
  DateTime? _lastMessageTime;

  // Controllers for transfer inputs
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _chargeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    print('DeviceConfigTab initState called');
    // Clear any old responses when initializing
    _deviceResponses.clear();
    print('About to load persistent states...');
    _loadPersistentStates();
    _listenToMqttMessages();
  }

  /// Load persistent toggle states from SharedPreferences
  Future<void> _loadPersistentStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load sound toggle state
      final soundState = prefs.getBool('toggle_sound');
      if (soundState != null) {
        _persistentToggleStates['sound'] = soundState;
        print('Loaded sound persistent state: $soundState');
      }

      // Load key toggle state
      final keyState = prefs.getBool('toggle_key');
      if (keyState != null) {
        _persistentToggleStates['key'] = keyState;
        print('Loaded key persistent state: $keyState');
      }

      // Load auto_shutdown toggle state
      final autoShutdownState = prefs.getBool('toggle_auto_shutdown');
      if (autoShutdownState != null) {
        _persistentToggleStates['auto_shutdown'] = autoShutdownState;
        print('Loaded auto_shutdown persistent state: $autoShutdownState');
      }

      // Load voltage_notify toggle state
      final voltageNotifyState = prefs.getBool('toggle_voltage_notify');
      if (voltageNotifyState != null) {
        _persistentToggleStates['voltage_notify'] = voltageNotifyState;
        print('Loaded voltage_notify persistent state: $voltageNotifyState');
      }

      // Load gps toggle state
      final gpsState = prefs.getBool('toggle_gps');
      if (gpsState != null) {
        _persistentToggleStates['gps'] = gpsState;
        print('Loaded gps persistent state: $gpsState');
      }

      // Load key_controlled toggle state
      final keyControlledState = prefs.getBool('toggle_key_controlled');
      if (keyControlledState != null) {
        _persistentToggleStates['key_controlled'] = keyControlledState;
        print('Loaded key_controlled persistent state: $keyControlledState');
      }

      print('Loaded persistent states: $_persistentToggleStates');

      // Trigger UI rebuild after loading states
      if (mounted) {
        setState(() {
          // This will cause the UI to rebuild with the loaded persistent states
        });
      }
    } catch (e) {
      print('Error loading persistent states: $e');
    }
  }

  /// Save persistent toggle state to SharedPreferences
  Future<void> _savePersistentState(String commandType, bool value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('toggle_$commandType', value);
      print('Saved $commandType persistent state to storage: $value');
    } catch (e) {
      print('Error saving $commandType persistent state: $e');
    }
  }

  /// Remove persistent toggle state from SharedPreferences
  Future<void> _removePersistentState(String commandType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('toggle_$commandType');
      print('Removed $commandType persistent state from storage');
    } catch (e) {
      print('Error removing $commandType persistent state: $e');
    }
  }

  void _listenToMqttMessages() {
    // Listen to MQTT messages from AppProvider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupMqttListener();
    });
  }

  void _setupMqttListener() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);

    // Retry mechanism in case MQTT handler isn't ready yet
    for (int i = 0; i < 5; i++) {
      if (appProvider.mqttHandler != null) {
        // Listen to device status updates which contain the raw messages
        appProvider.addListener(_onDeviceStatusChanged);

        // Subscribe to raw MQTT messages
        _rawMessageSubscription =
            appProvider.rawMessageStream?.listen((rawMessage) {
          if (mounted) {
            _addDeviceResponse(rawMessage);
          }
        });
        break;
      } else {
        await Future.delayed(Duration(milliseconds: 500));
      }
    }
  }

  void _onDeviceStatusChanged() {
    // This will be called when device status updates
    // For now, we'll simulate device responses
    // In a real implementation, you'd capture the actual MQTT payload here
    final appProvider = Provider.of<AppProvider>(context, listen: false);

    // Debug logging for device status changes
    print('Device status changed - online: ${appProvider.ds.online}');

    if (appProvider.ds.online) {
      // Simulate a device response when status changes
      _addDeviceResponse(
          '{"status":"Device status updated","online":${appProvider.ds.online}}');
    }
  }

  void _addDeviceResponse(String response) {
    final now = DateTime.now();

    // Filter out duplicate messages within 2 seconds
    if (_lastReceivedMessage == response &&
        _lastMessageTime != null &&
        now.difference(_lastMessageTime!).inSeconds < 2) {
      return; // Skip duplicate
    }

    // Filter out non-JSON responses or empty responses
    if (response.trim().isEmpty || !response.trim().startsWith('{')) {
      return;
    }

    _lastReceivedMessage = response;
    _lastMessageTime = now;

    setState(() {
      _deviceResponses.insert(0, response); // Add to beginning
      if (_deviceResponses.length > 20) {
        _deviceResponses.removeLast(); // Keep only last 20 messages
      }
    });
  }

  @override
  void dispose() {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    appProvider.removeListener(_onDeviceStatusChanged);
    _rawMessageSubscription?.cancel();
    _mobileController.dispose();
    _chargeController.dispose();
    super.dispose();
  }

  /// Formats device number to show only last 6 digits with asterisks for previous digits
  String _formatDeviceNumber(String deviceNumber) {
    if (deviceNumber.isEmpty) return 'N/A';

    if (deviceNumber.length <= 6) {
      return deviceNumber;
    }

    // Show asterisks for all but last 6 digits
    final hiddenLength = deviceNumber.length - 6;
    final asterisks = '*' * hiddenLength;
    final lastSixDigits = deviceNumber.substring(deviceNumber.length - 6);

    return '$asterisks$lastSixDigits';
  }

  /// Formats renter field - shows "Pool Broker: Байхгүй" if empty, otherwise shows the renter domain name
  String _formatRenterInfo(String renter) {
    if (renter.isEmpty) {
      return 'Pool Broker: Байхгүй';
    }
    return 'Pool Broker: $renter';
  }

  /// Formats firmware version for display
  String _formatFirmwareVersion(String version) {
    if (version.isEmpty) {
      return 'Firmware хувилбар: N/A';
    }
    return 'Firmware хувилбар: $version';
  }

  /// Builds a compact toggle button widget
  Widget _buildToggleButton(
    BuildContext context,
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Expanded(
      child: Container(
        padding: EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: FlutterFlowTheme.of(context).bodyText2.override(
                    fontFamily: 'Roboto',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 4),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: FlutterFlowTheme.of(context).secondaryColor,
              inactiveThumbColor: Colors.grey,
              inactiveTrackColor: Colors.grey.withValues(alpha: 0.3),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ],
        ),
      ),
    );
  }

  /// Gets the current toggle state (local override or device status)
  bool _getToggleState(String commandType, bool deviceValue) {
    // For sound, key, auto_shutdown, voltage_notify, gps, and key_controlled toggles, always use persistent state instead of device status
    if (commandType == 'sound' ||
        commandType == 'key' ||
        commandType == 'auto_shutdown' ||
        commandType == 'voltage_notify' ||
        commandType == 'gps' ||
        commandType == 'key_controlled') {
      // Always prioritize local state first, then persistent state
      bool finalValue = _localToggleStates[commandType] ??
          (_persistentToggleStates[commandType] ?? false);
      print(
          '$commandType toggle state (persistent) - Local: ${_localToggleStates[commandType]}, Persistent: ${_persistentToggleStates[commandType]}, Final: $finalValue, PersistentMap: $_persistentToggleStates');
      return finalValue;
    }

    // For other toggles, use normal logic
    bool hasLocalState = _localToggleStates.containsKey(commandType);
    bool finalValue = _localToggleStates[commandType] ?? deviceValue;

    // Debug logging for other toggles
    if (hasLocalState) {
      print(
          '$commandType toggle state - Local: ${_localToggleStates[commandType]}, Device: $deviceValue, Final: $finalValue');
    }

    return finalValue;
  }

  /// Sends transfer command to device
  Future<void> _sendTransferCommand(
    BuildContext context,
    AppProvider appProvider,
    User? user,
    String mobileNumber,
    int chargeAmount,
  ) async {
    if (user != null && appProvider.mqttHandler != null) {
      try {
        String command = 'unitel:$mobileNumber $chargeAmount';

        print('Sending transfer command: $command');
        bool result =
            await appProvider.mqttHandler!.sendDeviceCommand(user, command);
        print('Transfer command result: $result');

        if (result) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Transfer command sent successfully: $mobileNumber - $chargeAmount'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
          print('Transfer command sent successfully: $command');

          // Clear the input fields after successful transfer
          _mobileController.clear();
          _chargeController.clear();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to send transfer command'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
          print('Failed to send transfer command');
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending transfer command: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
        print('Error sending transfer command: $e');
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Device not connected'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      print('User or MQTT handler is null');
    }
  }

  /// Sends toggle command to device with optimistic update
  Future<void> _sendToggleCommand(
    BuildContext context,
    AppProvider appProvider,
    User? user,
    bool value,
    String commandType,
  ) async {
    // Optimistically update local state
    print('About to update state for $commandType to $value');
    setState(() {
      _localToggleStates[commandType] = value;

      // For sound, key, auto_shutdown, voltage_notify, gps, and key_controlled toggles, also save to persistent state
      if (commandType == 'sound' ||
          commandType == 'key' ||
          commandType == 'auto_shutdown' ||
          commandType == 'voltage_notify' ||
          commandType == 'gps' ||
          commandType == 'key_controlled') {
        _persistentToggleStates[commandType] = value;
        print('Saved $commandType persistent state: $value');
        print('Local states: $_localToggleStates');
        print('Persistent states: $_persistentToggleStates');

        // Save to SharedPreferences for persistence across sessions
        _savePersistentState(commandType, value);
      }
    });
    print('State updated for $commandType');

    if (user != null && appProvider.mqttHandler != null) {
      try {
        String command;
        if (commandType == 'sound') {
          // Special case for sound: use space instead of underscore
          command = value ? 'sound on' : 'sound off';
        } else if (commandType == 'key') {
          // Special case for key: use space instead of underscore
          command = value ? 'key on' : 'key off';
        } else if (commandType == 'auto_shutdown') {
          // Special case for auto_shutdown: use space instead of underscore
          command = value ? 'auto_shutdown on' : 'auto_shutdown off';
        } else if (commandType == 'voltage_notify') {
          // Special case for voltage_notify: use 'notify' instead of 'voltage_notify'
          command = value ? 'notify on' : 'notify off';
        } else if (commandType == 'gps') {
          // Special case for gps: use space instead of underscore
          command = value ? 'gps on' : 'gps off';
        } else if (commandType == 'key_controlled') {
          // Special case for key_controlled: use 'geely_atlas' instead of 'key_controlled'
          command = value ? 'geely_atlas on' : 'geely_atlas off';
        } else {
          // Default pattern for other commands
          command = value ? '${commandType}_on' : '${commandType}_off';
        }

        print('Sending MQTT command: $command for $commandType');
        bool result =
            await appProvider.mqttHandler!.sendDeviceCommand(user, command);
        print('MQTT command result: $result for $commandType');

        if (result) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  '${commandType.replaceAll('_', ' ')} ${value ? 'enabled' : 'disabled'} successfully'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
          print('$commandType command sent successfully: $command');

          // Clear local state after successful command - let device status take over
          // For sound, key, auto_shutdown, voltage_notify, gps, and key_controlled toggles, don't clear local state since we rely on persistent state
          if (commandType != 'sound' &&
              commandType != 'key' &&
              commandType != 'auto_shutdown' &&
              commandType != 'voltage_notify' &&
              commandType != 'gps' &&
              commandType != 'key_controlled') {
            int delaySeconds = 2;
            Future.delayed(Duration(seconds: delaySeconds), () {
              if (mounted) {
                setState(() {
                  print(
                      'Clearing local state for $commandType after $delaySeconds seconds');
                  _localToggleStates.remove(commandType);
                });
              }
            });
          } else {
            // For sound, key, auto_shutdown, voltage_notify, gps, and key_controlled toggles, clear local state immediately since we have persistent state
            Future.delayed(Duration(seconds: 1), () {
              if (mounted) {
                setState(() {
                  print(
                      'Clearing local state for $commandType toggle, keeping persistent state');
                  _localToggleStates.remove(commandType);
                });
              }
            });
          }
        } else {
          // Revert optimistic update on failure
          setState(() {
            _localToggleStates.remove(commandType);

            // For sound, key, auto_shutdown, voltage_notify, gps, and key_controlled toggles, DON'T revert persistent state since MQTT message was sent
            // The command "failure" might just be a return value issue, not actual failure
            if (commandType == 'sound' ||
                commandType == 'key' ||
                commandType == 'auto_shutdown' ||
                commandType == 'voltage_notify' ||
                commandType == 'gps' ||
                commandType == 'key_controlled') {
              print(
                  '$commandType command reported as failed, but keeping persistent state since MQTT was sent');
            }
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to change ${commandType.replaceAll('_', ' ')} setting'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
          print('Failed to send $commandType command');
        }
      } catch (e, stackTrace) {
        // Revert optimistic update on error
        print('ERROR in _sendToggleCommand for $commandType: $e');
        print('Stack trace: $stackTrace');
        setState(() {
          _localToggleStates.remove(commandType);

          // For sound, key, auto_shutdown, voltage_notify, gps, and key_controlled toggles, DON'T revert persistent state unless it's a critical error
          if (commandType == 'sound' ||
              commandType == 'key' ||
              commandType == 'auto_shutdown' ||
              commandType == 'voltage_notify' ||
              commandType == 'gps' ||
              commandType == 'key_controlled') {
            print(
                '$commandType command had error, but keeping persistent state: $e');
          }
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Error changing ${commandType.replaceAll('_', ' ')} setting: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
        print('Error sending $commandType command: $e');
      }
    } else {
      // Revert optimistic update if no connection
      setState(() {
        _localToggleStates.remove(commandType);

        // For sound, key, auto_shutdown, voltage_notify, gps, and key_controlled toggles, revert persistent state only if no connection
        if (commandType == 'sound' ||
            commandType == 'key' ||
            commandType == 'auto_shutdown' ||
            commandType == 'voltage_notify' ||
            commandType == 'gps' ||
            commandType == 'key_controlled') {
          _persistentToggleStates.remove(commandType);
          print('Reverted $commandType persistent state due to no connection');

          // Also remove from SharedPreferences
          _removePersistentState(commandType);
        }
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Device not connected'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      print('User or MQTT handler is null');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final user = appProvider.authClient;
        final deviceStatus = appProvider.ds;

        // Find the default device from user's devices list
        String deviceNumber = '';
        String renter = '';
        if (user?.devices != null && user!.devices!.isNotEmpty) {
          final defaultDevice = user.devices!.firstWhere(
            (device) => device.isDefault == true,
            orElse: () =>
                user.devices!.first, // Fallback to first device if no default
          );
          deviceNumber = defaultDevice.deviceNumber;
          renter = defaultDevice.renter;
        }

        final firmwareVersion = deviceStatus.ver;
        final formattedDeviceNumber = _formatDeviceNumber(deviceNumber);
        final formattedRenterInfo = _formatRenterInfo(renter);
        final formattedFirmwareVersion =
            _formatFirmwareVersion(firmwareVersion);

        return Padding(
          padding: EdgeInsetsDirectional.fromSTEB(0, 24, 0, 0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                // Device Number Display at the top
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 0),
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: FlutterFlowTheme.of(context).alternate,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Device Number',
                          style: FlutterFlowTheme.of(context)
                              .bodyText2
                              .override(
                                fontFamily: 'Roboto',
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color:
                                    FlutterFlowTheme.of(context).secondaryText,
                              ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          formattedDeviceNumber,
                          style: FlutterFlowTheme.of(context).title3.override(
                                fontFamily: 'Roboto',
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 1.2,
                              ),
                        ),
                        SizedBox(height: 12),
                        Text(
                          formattedRenterInfo,
                          style: FlutterFlowTheme.of(context)
                              .bodyText1
                              .override(
                                fontFamily: 'Roboto',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: FlutterFlowTheme.of(context).primaryText,
                              ),
                        ),
                        SizedBox(height: 12),
                        // Firmware version with update button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                formattedFirmwareVersion,
                                style: FlutterFlowTheme.of(context)
                                    .bodyText1
                                    .override(
                                      fontFamily: 'Roboto',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                    ),
                              ),
                            ),
                            SizedBox(width: 12),
                            // Update and Restart buttons
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ElevatedButton(
                                  onPressed: () async {
                                    // Send update command to device
                                    if (user != null &&
                                        appProvider.mqttHandler != null) {
                                      try {
                                        bool result = await appProvider
                                            .mqttHandler!
                                            .sendDeviceCommand(user, 'update');

                                        if (result) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Update command sent successfully'),
                                              backgroundColor: Colors.green,
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                          print(
                                              'Update command sent successfully');
                                        } else {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Failed to send update command'),
                                              backgroundColor: Colors.red,
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                          print(
                                              'Failed to send update command');
                                        }
                                      } catch (e) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                                'Error sending update command: $e'),
                                            backgroundColor: Colors.red,
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                        print(
                                            'Error sending update command: $e');
                                      }
                                    } else {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text('Device not connected'),
                                          backgroundColor: Colors.orange,
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                      print('User or MQTT handler is null');
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        FlutterFlowTheme.of(context)
                                            .secondaryColor,
                                    foregroundColor: Colors.white,
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        12, 8, 12, 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    elevation: 2,
                                  ),
                                  child: Text(
                                    'Update',
                                    style: TextStyle(
                                      fontFamily: 'Roboto',
                                      fontSize: 11,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 8),
                                ElevatedButton(
                                  onPressed: () async {
                                    // Send restart command to device
                                    if (user != null &&
                                        appProvider.mqttHandler != null) {
                                      try {
                                        bool result = await appProvider
                                            .mqttHandler!
                                            .sendDeviceCommand(user, 'restart');

                                        if (result) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Restart command sent successfully'),
                                              backgroundColor: Colors.green,
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                          print(
                                              'Restart command sent successfully');
                                        } else {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Failed to send restart command'),
                                              backgroundColor: Colors.red,
                                              duration: Duration(seconds: 2),
                                            ),
                                          );
                                          print(
                                              'Failed to send restart command');
                                        }
                                      } catch (e) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                                'Error sending restart command: $e'),
                                            backgroundColor: Colors.red,
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                        print(
                                            'Error sending restart command: $e');
                                      }
                                    } else {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text('Device not connected'),
                                          backgroundColor: Colors.orange,
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                      print('User or MQTT handler is null');
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    foregroundColor: Colors.white,
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        12, 8, 12, 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    elevation: 2,
                                  ),
                                  child: Text(
                                    'Restart',
                                    style: TextStyle(
                                      fontFamily: 'Roboto',
                                      fontSize: 11,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        // Additional toggle buttons in grid layout
                        Column(
                          children: [
                            // Row 1: Key, Automatic Shutdown, Key Controlled
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _buildToggleButton(
                                  context,
                                  'Key',
                                  _getToggleState('key',
                                      false), // Remove device status dependency
                                  (value) => _sendToggleCommand(
                                      context, appProvider, user, value, 'key'),
                                ),
                                _buildToggleButton(
                                  context,
                                  'Auto Shutdown',
                                  _getToggleState('auto_shutdown',
                                      false), // Remove device status dependency
                                  (value) => _sendToggleCommand(
                                      context,
                                      appProvider,
                                      user,
                                      value,
                                      'auto_shutdown'),
                                ),
                                _buildToggleButton(
                                  context,
                                  'Key Controlled',
                                  _getToggleState('key_controlled',
                                      false), // Remove device status dependency
                                  (value) => _sendToggleCommand(
                                      context,
                                      appProvider,
                                      user,
                                      value,
                                      'key_controlled'),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            // Row 2: Voltage Notify, GPS, Sound
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                _buildToggleButton(
                                  context,
                                  'Voltage Notify',
                                  _getToggleState('voltage_notify',
                                      false), // Remove device status dependency
                                  (value) => _sendToggleCommand(
                                      context,
                                      appProvider,
                                      user,
                                      value,
                                      'voltage_notify'),
                                ),
                                _buildToggleButton(
                                  context,
                                  'GPS',
                                  _getToggleState('gps',
                                      false), // Remove device status dependency
                                  (value) => _sendToggleCommand(
                                      context, appProvider, user, value, 'gps'),
                                ),
                                _buildToggleButton(
                                  context,
                                  'Sound',
                                  _getToggleState(
                                      'sound', false), // Remove rel2 dependency
                                  (value) => _sendToggleCommand(context,
                                      appProvider, user, value, 'sound'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // Transfer Section
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: FlutterFlowTheme.of(context).alternate,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Mobile Transfer',
                          style: FlutterFlowTheme.of(context).title3.override(
                                fontFamily: 'Roboto',
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        SizedBox(height: 16),
                        // Mobile Number Input
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Mobile Number',
                              style: FlutterFlowTheme.of(context)
                                  .bodyText2
                                  .override(
                                    fontFamily: 'Roboto',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryText,
                                  ),
                            ),
                            SizedBox(height: 8),
                            TextFormField(
                              controller: _mobileController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                hintText:
                                    'Enter mobile number (e.g., 88392933)',
                                hintStyle: TextStyle(
                                  fontFamily: 'Roboto',
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color:
                                        FlutterFlowTheme.of(context).alternate,
                                    width: 1,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color:
                                        FlutterFlowTheme.of(context).alternate,
                                    width: 1,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryColor,
                                    width: 2,
                                  ),
                                ),
                                contentPadding: EdgeInsetsDirectional.fromSTEB(
                                    12, 12, 12, 12),
                              ),
                              style: TextStyle(
                                fontFamily: 'Roboto',
                                fontSize: 14,
                                color: FlutterFlowTheme.of(context).primaryText,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        // Charge Amount Input
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Charge Amount (Max: 2000)',
                              style: FlutterFlowTheme.of(context)
                                  .bodyText2
                                  .override(
                                    fontFamily: 'Roboto',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryText,
                                  ),
                            ),
                            SizedBox(height: 8),
                            TextFormField(
                              controller: _chargeController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                hintText: 'Enter amount (1-2000)',
                                hintStyle: TextStyle(
                                  fontFamily: 'Roboto',
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color:
                                        FlutterFlowTheme.of(context).alternate,
                                    width: 1,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color:
                                        FlutterFlowTheme.of(context).alternate,
                                    width: 1,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryColor,
                                    width: 2,
                                  ),
                                ),
                                contentPadding: EdgeInsetsDirectional.fromSTEB(
                                    12, 12, 12, 12),
                              ),
                              style: TextStyle(
                                fontFamily: 'Roboto',
                                fontSize: 14,
                                color: FlutterFlowTheme.of(context).primaryText,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 20),
                        // Transfer Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () async {
                              String mobileNumber =
                                  _mobileController.text.trim();
                              String chargeText = _chargeController.text.trim();

                              // Validation
                              if (mobileNumber.isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Please enter mobile number'),
                                    backgroundColor: Colors.orange,
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                                return;
                              }

                              if (chargeText.isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Please enter charge amount'),
                                    backgroundColor: Colors.orange,
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                                return;
                              }

                              int? chargeAmount = int.tryParse(chargeText);
                              if (chargeAmount == null ||
                                  chargeAmount <= 0 ||
                                  chargeAmount > 2000) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                        'Charge amount must be between 1 and 2000'),
                                    backgroundColor: Colors.orange,
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                                return;
                              }

                              // Send transfer command
                              await _sendTransferCommand(
                                context,
                                appProvider,
                                user,
                                mobileNumber,
                                chargeAmount,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  FlutterFlowTheme.of(context).secondaryColor,
                              foregroundColor: Colors.white,
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  16, 12, 16, 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 2,
                            ),
                            child: Text(
                              'Transfer',
                              style: TextStyle(
                                fontFamily: 'Roboto',
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Title
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
                  child: Text(
                    FFLocalizations.of(context).getText(
                      'device_config_placeholder' /* Device Configuration */,
                    ),
                    style: FlutterFlowTheme.of(context).title2,
                  ),
                ),

                // Device Response Window
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(24, 12, 24, 24),
                  child: Container(
                    width: double.infinity,
                    height: 200,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: FlutterFlowTheme.of(context).alternate,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Container(
                          width: double.infinity,
                          padding:
                              EdgeInsetsDirectional.fromSTEB(16, 12, 16, 8),
                          decoration: BoxDecoration(
                            color:
                                FlutterFlowTheme.of(context).primaryBackground,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Device Response',
                                style: FlutterFlowTheme.of(context)
                                    .bodyText1
                                    .override(
                                      fontFamily: 'Roboto',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                    ),
                              ),
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'Device: ${_formatDeviceNumber(user?.device?.deviceNumber ?? '')}',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyText2
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          color: FlutterFlowTheme.of(context)
                                              .secondaryText,
                                        ),
                                  ),
                                  SizedBox(width: 8),
                                  InkWell(
                                    onTap: () {
                                      setState(() {
                                        _deviceResponses.clear();
                                      });
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content:
                                              Text('Response history cleared'),
                                          backgroundColor: Colors.blue,
                                          duration: Duration(seconds: 1),
                                        ),
                                      );
                                    },
                                    child: Container(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          6, 4, 6, 4),
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.red.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                          color:
                                              Colors.red.withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.clear,
                                            size: 12,
                                            color: Colors.red[700],
                                          ),
                                          SizedBox(width: 2),
                                          Text(
                                            'Clear',
                                            style: TextStyle(
                                              fontFamily: 'Roboto',
                                              fontSize: 10,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.red[700],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // Response content
                        Expanded(
                          child: Container(
                            width: double.infinity,
                            padding:
                                EdgeInsetsDirectional.fromSTEB(16, 8, 16, 16),
                            child: _deviceResponses.isEmpty
                                ? Center(
                                    child: Text(
                                      'No device responses yet.\nSend a command to see responses here.',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontFamily: 'Roboto',
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  )
                                : ListView.builder(
                                    itemCount: _deviceResponses.length,
                                    itemBuilder: (context, index) {
                                      final response = _deviceResponses[index];
                                      final timestamp = DateTime.now()
                                          .subtract(Duration(minutes: index))
                                          .toString()
                                          .substring(11, 19);

                                      return Container(
                                        margin: EdgeInsetsDirectional.fromSTEB(
                                            0, 2, 0, 2),
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            8, 6, 8, 6),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[50],
                                          borderRadius:
                                              BorderRadius.circular(4),
                                          border: Border.all(
                                            color: Colors.grey[300]!,
                                            width: 0.5,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              timestamp,
                                              style: TextStyle(
                                                fontFamily: 'Roboto',
                                                fontSize: 10,
                                                color: Colors.grey[600],
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            SizedBox(height: 2),
                                            Text(
                                              response,
                                              style: TextStyle(
                                                fontFamily: 'Courier',
                                                fontSize: 11,
                                                color: Colors.black87,
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
