import 'package:flutter/material.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import 'tabs/profile_tab.dart';
import 'tabs/device_config_tab.dart';
import 'tabs/security_tab.dart';
import 'tabs/devices_tab.dart';

class BasicProfileWidget extends StatefulWidget {
  const BasicProfileWidget({Key? key}) : super(key: key);

  @override
  _BasicProfileWidgetState createState() => _BasicProfileWidgetState();
}

class _BasicProfileWidgetState extends State<BasicProfileWidget> {
  final _unfocusNode = FocusNode();
  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void dispose() {
    _unfocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      appBar: AppBar(
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        automaticallyImplyLeading: false,
        title: Text(
          FFLocalizations.of(context).getText(
            'zruwhuzw' /* Personal Infomation */,
          ),
          style: FlutterFlowTheme.of(context).title1,
        ),
        actions: [],
        centerTitle: false,
        elevation: 0,
      ),
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(_unfocusNode),
          child: Stack(
            children: [
              DefaultTabController(
                length: 4,
                initialIndex: 0,
                child: Column(
                  children: [
                    TabBar(
                      labelColor: FlutterFlowTheme.of(context).primaryText,
                      labelStyle: FlutterFlowTheme.of(context).bodyText1,
                      indicatorColor:
                          FlutterFlowTheme.of(context).secondaryColor,
                      tabs: [
                        Tab(
                          text: FFLocalizations.of(context).getText(
                            '1ipyytif' /* Profile */,
                          ),
                        ),
                        Tab(
                          text: FFLocalizations.of(context).getText(
                            'mc4mpuxk' /* Device Config */,
                          ),
                        ),
                        Tab(
                          text: FFLocalizations.of(context).getText(
                            'bxr5e3d1' /* Security */,
                          ),
                        ),
                        Tab(
                          text: FFLocalizations.of(context).getText(
                            'a03hzgid' /* Devices */,
                          ),
                        ),
                      ],
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          ProfileTab(),
                          DeviceConfigTab(),
                          SecurityTab(),
                          DevicesTab(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
