class HttpResultException implements Exception {
  final int statusCode;
  final String message;
  final String title;
  final bool success;

  String toString() {
    return 'message:{$message}, title:{$title}, statusCode:{$statusCode}';
  }

  @override
  HttpResultException(
      {required this.message,
      required this.statusCode,
      required this.title,
      required this.success})
      : super();
}
