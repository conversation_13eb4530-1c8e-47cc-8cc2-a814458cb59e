// lib/widgets/custom_app_bar.dart

import 'package:flutter/material.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;

  const CustomAppBar({Key? key, required this.title}) : super(key: key);

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      automaticallyImplyLeading: false,
      title: Text(
        FFLocalizations.of(context).getText(
          'qwth9wth' /* REMOTE CAR CONTROL SYSTEM */,
        ),
        style: FlutterFlowTheme.of(context).title2.override(
              fontFamily: 'Roboto',
              color: FlutterFlowTheme.of(context).primaryText,
              fontSize: 22,
            ),
      ),
      centerTitle: false,
      elevation: 0,
    );
  }
}
