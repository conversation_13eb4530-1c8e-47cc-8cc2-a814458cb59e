// lib/components/status_display.dart

import 'package:aslaa/models/device.dart';
import 'package:flutter/material.dart';

class StatusDisplay extends StatelessWidget {
  final String userStatus;
  final Color userStatusColor;
  final double speed;
  final String speedUnit;
  final Device? device;
  final bool isConnected;

  const StatusDisplay({
    Key? key,
    required this.userStatus,
    required this.userStatusColor,
    required this.speed,
    required this.speedUnit,
    this.device,
    required this.isConnected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 5.0), // adjust the value as needed
      child: Column(
        children: [
          // User Status Display
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.person, // Example icon
                color: userStatusColor,
                size: 16,
              ),
              SizedBox(width: 4),
              Text(
                userStatus,
                style: TextStyle(
                  color: userStatusColor,
                  fontSize: 18, // Adjust the font size as needed
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          // Device Speed Display
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.speed, // Example icon
                color: Theme.of(context).colorScheme.secondary,
                size: 16,
              ),
              SizedBox(width: 4),
              TweenAnimationBuilder(
                tween: Tween<double>(begin: speed - 2, end: speed),
                duration: Duration(seconds: 3),
                builder: (BuildContext context, double value, Widget? child) {
                  return Text(
                    'Speed: ${value.toStringAsFixed(1)} $speedUnit',
                    style: Theme.of(context).textTheme.bodyMedium,
                  );
                },
              ),
            ],
          ),

          // Device Information Display
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isConnected ? Icons.check_circle : Icons.cancel,
                color: isConnected ? Colors.green : Colors.red,
                size: 16,
              ),
              SizedBox(width: 4),
              Text(
                device != null
                    ? ((device!.deviceName != ''
                            ? device!.deviceName
                            : device!.deviceNumber) +
                        ' : ${device!.type.toUpperCase()}')
                    : 'Device Is Not Available',
                style: TextStyle(
                  fontSize: 18, // Adjusted the font size back to 12
                  color: isConnected
                      ? Colors.green
                      : Colors.red, // Green when connected, red otherwise
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
