// lib/widgets/status_indicators_widget.dart

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../models/device.dart';
import '../components/signal_strength_indicator.dart';
import 'dart:async';

class StatusIndicatorsWidget extends StatefulWidget {
  final DeviceStatus ds;
  final int rssi;
  final String? simBalance;
  final String? simExpiredDate;
  final VoidCallback onSimCardTap;

  const StatusIndicatorsWidget({
    Key? key,
    required this.ds,
    required this.rssi,
    this.simBalance,
    this.simExpiredDate,
    required this.onSimCardTap,
  }) : super(key: key);

  @override
  _StatusIndicatorsWidgetState createState() => _StatusIndicatorsWidgetState();
}

class _StatusIndicatorsWidgetState extends State<StatusIndicatorsWidget> {
  // Local state to track which tooltip is visible
  String? _visibleTooltip;
  Timer? _tooltipTimer;
  OverlayEntry? _overlayEntry;

  // Simple method to show tooltip
  void _showTooltip(String message) {
    print("Tooltip tapped: $message"); // Debug print

    // Remove any existing overlay
    _overlayEntry?.remove();
    _overlayEntry = null;

    // Cancel any existing timer
    _tooltipTimer?.cancel();

    // Create a new overlay entry
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return Positioned(
          left: 140, // Position to the right of the left panel
          top: 100, // Adjust as needed
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                message,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: 'PorscheNumber',
                ),
              ),
            ),
          ),
        );
      },
    );

    // Insert the overlay entry
    Overlay.of(context).insert(_overlayEntry!);

    // Set timer to remove overlay after 2 seconds
    _tooltipTimer = Timer(Duration(seconds: 2), () {
      _overlayEntry?.remove();
      _overlayEntry = null;
    });
  }

  @override
  void dispose() {
    _tooltipTimer?.cancel();
    _overlayEntry?.remove();
    super.dispose();
  }

  String getFormatedNumber(double value) {
    return value.toStringAsFixed(1);
  }

  Color getSimCardColor(BuildContext context) {
    if (widget.simExpiredDate != null) {
      try {
        print('Parsing date: ${widget.simExpiredDate}');

        // Determine the date format based on the input
        DateTime expirationDate;
        if (RegExp(r'^\d{4}/\d{2}/\d{2}$').hasMatch(widget.simExpiredDate!)) {
          // Four-digit year format
          expirationDate =
              DateFormat('yyyy/MM/dd').parseStrict(widget.simExpiredDate!);
        } else if (RegExp(r'^\d{2}/\d{2}/\d{2}$')
            .hasMatch(widget.simExpiredDate!)) {
          // Two-digit year format
          int year = int.parse(widget.simExpiredDate!.split('/')[0]);
          final currentYear = DateTime.now().year;
          final century = (currentYear ~/ 100) * 100;
          year += century;
          String adjustedDate = '$year/${widget.simExpiredDate!.substring(5)}';
          expirationDate = DateFormat('yyyy/MM/dd').parseStrict(adjustedDate);
        } else {
          throw FormatException('Unsupported date format');
        }

        final now = DateTime.now();

        print('Expiration date: $expirationDate');
        print('Current date: $now');
        print(
            'Days until expiration: ${expirationDate.difference(now).inDays}');

        if (expirationDate.isBefore(now)) {
          return Colors.red;
        } else if (expirationDate.difference(now).inDays <= 7) {
          return const Color.fromARGB(255, 255, 223, 0);
        } else {
          return FlutterFlowTheme.of(context).primaryText;
        }
      } catch (e) {
        print('Error parsing date: $e');
      }
    }
    return FlutterFlowTheme.of(context).primaryText;
  }

  @override
  Widget build(BuildContext context) {
    // Determine base color based on brightness
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color defaultColor = isDarkMode ? Colors.white : Colors.black;

    return Material(
      color: Colors.transparent,
      child: Container(
        width: 120, // Keep original width
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
        decoration: BoxDecoration(
          color:
              FlutterFlowTheme.of(context).primaryBackground.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // SIGNAL STRENGTH
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _showTooltip('Сигналын хүч'),
                borderRadius: BorderRadius.circular(4),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: SignalStrengthIndicator(
                    rssi: widget.rssi,
                  ),
                ),
              ),
            ),

            // TEMPERATURE
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _showTooltip('Температур'),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      FaIcon(
                        FontAwesomeIcons.thermometer,
                        color: FlutterFlowTheme.of(context).primaryText,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TweenAnimationBuilder(
                          tween: Tween<double>(
                            begin: widget.ds.temp - 2,
                            end: widget.ds.temp + 1,
                          ),
                          duration: const Duration(seconds: 3),
                          builder: (BuildContext context, double value,
                              Widget? child) {
                            return Text(
                              '${getFormatedNumber(value)}°C',
                              style: TextStyle(
                                fontFamily: 'PorscheNumber',
                                fontSize: 18,
                                color: defaultColor,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // HUMIDITY
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _showTooltip('Чийгшил'),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      FaIcon(
                        FontAwesomeIcons.droplet,
                        color: FlutterFlowTheme.of(context).primaryText,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TweenAnimationBuilder(
                          tween: Tween<double>(
                            begin: widget.ds.hum - 2,
                            end: widget.ds.hum + 1,
                          ),
                          duration: const Duration(seconds: 3),
                          builder: (BuildContext context, double value,
                              Widget? child) {
                            return Text(
                              '${getFormatedNumber(value)}%',
                              style: TextStyle(
                                fontFamily: 'PorscheNumber',
                                fontSize: 18,
                                color: defaultColor,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // BATTERY / VOLTAGE
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _showTooltip('Батарейны хүчдэл'),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.battery_charging_full_sharp,
                        color: (widget.ds.volt >= 12)
                            ? FlutterFlowTheme.of(context).secondaryColor
                            : Colors.red,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TweenAnimationBuilder(
                          tween: Tween<double>(
                            begin: widget.ds.volt.toDouble() - 3,
                            end: widget.ds.volt.toDouble(),
                          ),
                          duration: const Duration(seconds: 2),
                          curve: Curves.easeInOut,
                          builder: (BuildContext context, double value,
                              Widget? child) {
                            return Text(
                              '${value.toStringAsFixed(3)}V',
                              style: TextStyle(
                                fontFamily: 'PorscheNumber',
                                fontSize: 18,
                                color: defaultColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // SIM CARD BALANCE & EXPIRATION
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  // Show tooltip
                  _showTooltip('СИМ картын төлөв');

                  // Call the onSimCardTap callback to send the command
                  widget.onSimCardTap();
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.sim_card,
                        color: getSimCardColor(context),
                        size: 18,
                      ),
                      const SizedBox(width: 4),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.simBalance ?? 'IoT сим',
                            style: TextStyle(
                              fontFamily: 'PorscheNumber',
                              fontSize: 14,
                              color: defaultColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            widget.simExpiredDate ?? '10 жил',
                            style: TextStyle(
                              fontFamily: 'PorscheNumber',
                              fontSize: 14,
                              color: defaultColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
