// lib/widgets/car_display_widget.dart

import 'package:aslaa/components/car_top.dart';
import 'package:aslaa/components/chip_widget.dart';
import 'package:aslaa/components/moped.dart';
import 'package:aslaa/models/device.dart';
import 'package:flutter/material.dart';

class CarDisplayWidget extends StatelessWidget {
  final DeviceStatus ds;
  final bool loading;
  final String? uix;
  final AnimationController countdownController;
  final Key? key;

  const CarDisplayWidget({
    required this.ds,
    required this.loading,
    required this.uix,
    required this.countdownController,
    this.key,
  }) : super(key: key);

  String getFormatedNumber(double value) {
    return value.toStringAsFixed(1);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height * 1,
      decoration: BoxDecoration(
        color: Colors.transparent,
      ),
      child: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 1,
            child: loading
                ? Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(60, 60, 60, 60),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          strokeWidth: 10,
                        ),
                      ],
                    ),
                  )
                : (uix != null && uix!.toLowerCase() == 'moped')
                    ? MopedWidget(key: key, ds: ds)
                    : (uix != null && uix!.toLowerCase().contains('chip'))
                        ? ChipWidget(key: key, ds: ds)
                        : CarTopWidget(
                            key: key,
                            ds: ds,
                            countdownController: countdownController,
                          ),
          ),
        ],
      ),
    );
  }
}
