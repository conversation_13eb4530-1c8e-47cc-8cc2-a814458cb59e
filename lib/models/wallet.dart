import 'package:flutter/material.dart';

class Wallet {
  String bankAccount;
  String bankName;
  final String user;
  num currentBalance;
  List<dynamic>? transactions;
  num deposit;
  num withdraw;
  Wallet(
      {required this.bankAccount,
      required this.bankName,
      required this.user,
      required this.currentBalance,
      this.transactions,
      this.deposit = 0,
      this.withdraw = 0});

  factory Wallet.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return Wallet.fromVirtual();
    } else {
      List<dynamic>? transactions = json['transactions'];
      List<dynamic>? deposits =
          transactions?.where((e) => e['mode'] == 'deposit').toList();
      List<dynamic>? withdraws = transactions
          ?.where((e) =>
              e['mode'] == 'withdraw' &&
              e['description'] != null &&
              e['description'] != '')
          .toList();
      debugPrint('$deposits is deposits');
      num deposit = deposits?.length == 0
          ? 0
          : (deposits?.reduce((value, element) {
                return {"amount": (value?['amount'] + element?['amount'])};
              })?['amount'] ??
              0);

      num withdraw = withdraws?.length == 0
          ? 0
          : (withdraws?.reduce((value, element) {
                return {"amount": (value?['amount'] + element?['amount'])};
              })?['amount'] ??
              0);

      return Wallet(
        bankAccount: json['bankAccount'] ?? '',
        bankName: json['bankName'] ?? '',
        user: json['user'],
        currentBalance: json['currentBalance'],
        transactions: transactions,
        deposit: deposit,
        withdraw: withdraw,
      );
    }
  }
  factory Wallet.fromVirtual() {
    return Wallet(
        bankAccount: 'Bank Account',
        bankName: 'Bank Name',
        user: '',
        transactions: null,
        deposit: 0,
        withdraw: 0,
        currentBalance: 123456);
  }
}
