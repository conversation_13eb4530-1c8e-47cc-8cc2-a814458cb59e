// lib/components/version_button.dart

import 'package:flutter/material.dart';

class VersionButton extends StatelessWidget {
  final String version;

  const VersionButton({
    Key? key,
    required this.version,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get first 5 characters of version string, or use full string if shorter
    final displayVersion = version.isNotEmpty
        ? (version.length > 5 ? version.substring(0, 5) : version)
        : '1.0.0';

    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Icon(
            Icons.info_outline,
            color: Theme.of(context).colorScheme.secondary,
            size: 18,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              'V$displayVersion',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'PorscheNumber',
                    fontSize: 18,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
