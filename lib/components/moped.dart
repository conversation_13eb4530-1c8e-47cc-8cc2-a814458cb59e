import 'package:aslaa/models/device.dart';
import 'package:flutter/material.dart';

class MopedWidget extends StatefulWidget {
  final DeviceStatus? ds;
  const MopedWidget({Key? key, this.ds}) : super(key: key);

  @override
  _MopedWidgetState createState() => _MopedWidgetState();
}

class _MopedWidgetState extends State<MopedWidget> {
  String color = 'red';
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    DeviceStatus? ds = widget.ds;

    if (ds == null) {
      color = 'yellow';
    } else {
      color = ds.online ? 'black' : 'yellow';
      color = ds.sta == 1 ? 'green' : color;
    }

    bool isStaZero = ds?.sta == 0;
    bool isUnlock = ds?.unlock == true;

    return Stack(
      alignment: AlignmentDirectional(0, 0),
      children: [
        Image.asset(
          'assets/images/moped-$color.png',
          width: 194,
          height: 220,
          fit: BoxFit.cover,
        ),
        if (isStaZero)
          Image.asset(
            isUnlock
                ? 'assets/images/moped-unlock.png'
                : 'assets/images/moped-lock.png',
            width: 194,
            height: 220,
            fit: BoxFit.cover,
          ),
      ],
    );
  }
}
