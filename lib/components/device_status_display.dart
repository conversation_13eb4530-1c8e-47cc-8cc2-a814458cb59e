// lib/components/device_status_display.dart

import 'package:flutter/material.dart';

class DeviceStatusDisplay extends StatelessWidget {
  final double speed;
  final String speedUnit;
  final String deviceName;
  final bool isConnected;

  const DeviceStatusDisplay({
    Key? key,
    required this.speed,
    required this.speedUnit,
    required this.deviceName,
    required this.isConnected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Device Speed Display
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: speed - 2, end: speed),
              duration: Duration(seconds: 3),
              builder: (BuildContext context, double value, Widget? child) {
                return Text(
                  'Speed: ${value.toStringAsFixed(1)} $speedUnit',
                  style: Theme.of(context).textTheme.bodyMedium,
                );
              },
            ),
          ],
        ),
        // Device Connectivity Status
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              deviceName,
              style: TextStyle(
                fontSize: 12,
                color: isConnected ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
