// lib/widgets/signal_strength_indicator.dart

import 'package:flutter/material.dart';

class SignalStrengthIndicator extends StatelessWidget {
  final int rssi; // Changed to int

  const SignalStrengthIndicator({Key? key, required this.rssi})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    int signalLevel = getSignalLevel(rssi); // No casting needed

    Color signalColor;
    switch (signalLevel) {
      case 4:
        signalColor = Colors.green;
        break;
      case 3:
        signalColor = Colors.lightGreen;
        break;
      case 2:
        signalColor = Colors.yellow;
        break;
      case 1:
        signalColor = Colors.orange;
        break;
      default:
        signalColor = Colors.red;
    }

    // Build the signal bars
    List<Widget> bars = [];
    for (int i = 1; i <= 4; i++) {
      bars.add(Container(
        margin: EdgeInsets.symmetric(horizontal: 1.0),
        width: 4.0,
        height: i * 6.0,
        decoration: BoxDecoration(
          color: i <= signalLevel ? signalColor : Colors.grey[300],
          borderRadius: BorderRadius.circular(2.0),
        ),
      ));
    }

    return Tooltip(
      message: 'Signal Strength: ${getSignalDescription(signalLevel)}',
      child: Column(
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: bars,
          ),
          SizedBox(height: 4.0),
          Text(
            '${rssi} dBm', // Display as integer
            style: TextStyle(
              fontFamily: 'PorscheNumber',
              fontSize: 18.0,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  /// Maps RSSI value (0-30) to a signal level (0-4)
  int getSignalLevel(int rssi) {
    if (rssi >= 25) {
      return 4; // Excellent
    } else if (rssi >= 20) {
      return 3; // Good
    } else if (rssi >= 15) {
      return 2; // Fair
    } else if (rssi >= 5) {
      return 1; // Weak
    } else {
      return 0; // No Signal
    }
  }

  /// Provides a description based on signal level
  String getSignalDescription(int signalLevel) {
    switch (signalLevel) {
      case 4:
        return 'Excellent';
      case 3:
        return 'Good';
      case 2:
        return 'Fair';
      case 1:
        return 'Weak';
      default:
        return 'No Signal';
    }
  }
}
