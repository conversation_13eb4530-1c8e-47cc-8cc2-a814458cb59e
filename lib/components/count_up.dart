import 'dart:async';

import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

class CountUpWidget extends StatefulWidget {
  int offset = 0;
  Function? onChange;
  List<int> priceTable;
  String mode;
  CountUpWidget(
      {Key? key,
      this.offset = 0,
      this.onChange,
      required this.priceTable,
      required this.mode})
      : super(key: key);
  @override
  _CountUpWidgetState createState() => _CountUpWidgetState();
}

class _CountUpWidgetState extends State<CountUpWidget> {
  Timer? _timer;
  num offset = 0;
  num days = 0;
  num hours = 0;
  num minutes = 0;
  num seconds = 0;
  num usedFee = 0;
  List<int>? priceTable;
  String _displayTime() {
    return days.toString().padLeft(2, '0') +
        ':' +
        hours.toString().padLeft(2, '0') +
        ':' +
        minutes.toString().padLeft(2, '0') +
        ':' +
        seconds.toString().padLeft(2, '0');
  }

  void initState() {
    super.initState();

    if (mounted) {
      priceTable = widget.priceTable;
      offset = widget.offset;
      _timer = Timer.periodic(Duration(seconds: 1), (timer) {
        setState(() {
          offset = offset + 1;
          days = (offset / 24 / 3600).floor();

          hours = ((offset - days * 24 * 3600) / 3600).floor();
          minutes = ((offset - days * 3600 * 24 - hours * 3600) / 60).floor();
          seconds = (offset - days * 3600 * 24 - hours * 3600 - minutes * 60);

          if (seconds == 1) {
            num price = 0;
            if (widget.mode.toLowerCase() == 'minute') {
              price = ((offset / 60).floor() + 1) * priceTable![0];
            }
            if (widget.mode.toLowerCase() == 'hour') {
              price = ((offset / 3600).floor() + 1) * priceTable![1];
            }
            if (widget.mode.toLowerCase() == 'daily') {
              price = ((offset / 24 / 3600).floor() + 1) * priceTable![2];
            }
            usedFee = price;

            if (widget.onChange != null) {
              debugPrint(
                  '$usedFee is used fee and $price is price $offset is offset');
              widget.onChange!(offset, price);
            }
          }
        });
      });
    }
  }

  void dispose() {
    _timer?.cancel();
    debugPrint('disposed');
    super.dispose();
  }

  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsetsDirectional.fromSTEB(12, 12, 12, 12),
        child: Column(
          children: [
            Text(_displayTime(),
                style: FlutterFlowTheme.of(context).bodyText1.override(
                      fontFamily: 'Roboto',
                      color: FlutterFlowTheme.of(context).tertiaryColor,
                      fontSize: 32,
                    )),
            Text('-${getFormatedNumber(usedFee)}',
                style: FlutterFlowTheme.of(context).bodyText1.override(
                      fontFamily: 'Roboto',
                      color: FlutterFlowTheme.of(context).secondaryColor,
                      fontSize: 24,
                    )),
          ],
        ));
  }
}
