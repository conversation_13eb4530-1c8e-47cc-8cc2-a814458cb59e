// lib/components/gps_status_indicator.dart

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class GpsStatusIndicator extends StatelessWidget {
  final double latitude;
  final double longitude;
  final bool isVirtual;

  const GpsStatusIndicator({
    Key? key,
    required this.latitude,
    required this.longitude,
    required this.isVirtual,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Decide text color based on brightness
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color textColor = isDarkMode ? Colors.white : Colors.black;

    Color gpsColor;
    if (latitude > 0 && longitude > 0 && !isVirtual) {
      gpsColor = Theme.of(context).colorScheme.secondary;
    } else if (latitude == 0 || longitude == 0) {
      gpsColor = Colors.red; // Show red if missing coords
    } else {
      gpsColor = Theme.of(context).textTheme.bodyMedium?.color ?? Colors.grey;
    }

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          FaIcon(
            FontAwesomeIcons.satellite,
            color: gpsColor,
            size: 18,
          ),
          const SizedBox(width: 12),
          Text(
            'GPS',
            style: TextStyle(
              fontFamily: 'PorscheNumber',
              fontSize: 18,
              color: textColor, // Use textColor instead of fixed Colors.black
            ),
          ),
        ],
      ),
    );
  }
}
