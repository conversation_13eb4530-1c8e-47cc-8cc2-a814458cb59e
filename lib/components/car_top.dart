import 'package:flutter/material.dart';
import 'package:aslaa/models/device.dart';
import 'package:audioplayers/audioplayers.dart';

class CarTopWidget extends StatefulWidget {
  final DeviceStatus? ds;
  final AnimationController? countdownController;

  const CarTopWidget({Key? key, this.ds, this.countdownController})
      : super(key: key);

  @override
  _CarTopWidgetState createState() => _CarTopWidgetState();
}

class _CarTopWidgetState extends State<CarTopWidget>
    with SingleTickerProviderStateMixin {
  String color = 'black';
  int countdownValue = 0;
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _hasPlayedEngineSound = false;
  late AnimationController _doorAnimationController;

  @override
  void initState() {
    super.initState();

    _doorAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    widget.countdownController?.addListener(() {
      if (mounted) {
        setState(() {
          countdownValue = (widget.countdownController!.duration!.inSeconds *
                  (1 - widget.countdownController!.value))
              .ceil();
        });
      }
    });
  }

  @override
  void didUpdateWidget(CarTopWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Play engine sound if the car is started and sound hasn't been played yet
    if (widget.ds?.sta == 1 && !_hasPlayedEngineSound) {
      _playEngineSound();
      _hasPlayedEngineSound = true;
    } else if (widget.ds?.sta != 1) {
      _hasPlayedEngineSound = false;
    }

    // Animate doors based on unlock status
    if (widget.ds?.unlock == true) {
      _doorAnimationController.forward();
    } else {
      _doorAnimationController.reverse();
    }
  }

  void _playEngineSound() async {
    await _audioPlayer.play(AssetSource('audios/engine.mp3'));
  }

  @override
  void dispose() {
    widget.countdownController?.removeListener(() {});
    _audioPlayer.dispose();
    _doorAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    DeviceStatus? ds = widget.ds;

    if (ds == null) {
      color = 'black';
    } else {
      color = ds.online
          ? 'black'
          : ds.virtual
              ? 'black'
              : 'yellow';
      color = ds.sta == 1 ? 'green' : color;
    }

    bool isAnimating =
        widget.countdownController?.status == AnimationStatus.forward;

    return Stack(
      alignment: AlignmentDirectional.center,
      children: [
        Image.asset(
          'assets/images/car-$color-body.png',
          width: 388,
          height: 440,
          fit: BoxFit.cover,
        ),
        if (widget.ds?.sta == 1)
          Image.asset(
            'assets/images/car-front-light.png',
            width: 388,
            height: 440,
            fit: BoxFit.cover,
          ),
        RotationTransition(
          turns:
              Tween(begin: 0.0, end: 0.0833).animate(_doorAnimationController),
          alignment: Alignment(-0.25, -0.25),
          child: Image.asset(
            'assets/images/car-$color-l-door.png',
            width: 388,
            height: 440,
            fit: BoxFit.cover,
          ),
        ),
        RotationTransition(
          turns:
              Tween(begin: 0.0, end: -0.0833).animate(_doorAnimationController),
          alignment: Alignment(0.25, -0.25),
          child: Image.asset(
            'assets/images/car-$color-r-door.png',
            width: 388,
            height: 440,
            fit: BoxFit.cover,
          ),
        ),
        if (isAnimating || (widget.ds?.backlight == 1))
          Image.asset(
            'assets/images/car-back-light.png',
            width: 388,
            height: 440,
            fit: BoxFit.cover,
          ),
        if (isAnimating)
          Positioned(
            bottom: 95,
            child: Text(
              '$countdownValue',
              style: TextStyle(
                fontSize: 60,
                fontFamily: 'PorscheNumber',
                color: Colors.redAccent,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    blurRadius: 10.0,
                    color: Colors.black.withOpacity(0.5),
                    offset: Offset(2.0, 2.0),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
