import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'dart:async';
import 'package:flutter/services.dart'; // Import for HapticFeedback

class ScrollableMapButton extends StatefulWidget {
  final bool isRecording;
  final Future<void> Function() onPressed;

  ScrollableMapButton({
    Key? key,
    required this.isRecording,
    required this.onPressed,
  }) : super(key: key);

  @override
  _ScrollableMapButtonState createState() => _ScrollableMapButtonState();
}

class _ScrollableMapButtonState extends State<ScrollableMapButton>
    with TickerProviderStateMixin {
  Timer? _animationTimer;
  bool _showAnimation = false;

  @override
  void initState() {
    super.initState();
    if (widget.isRecording) {
      _startAnimationTimer();
    }
  }

  void _startAnimationTimer() {
    _showAnimation = true;
    _animationTimer = Timer(Duration(seconds: 3), () {
      setState(() {
        _showAnimation = false;
      });
    });
  }

  @override
  void dispose() {
    _animationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isRecording
          ? null
          : () async {
              HapticFeedback.lightImpact(); // Add HapticFeedback
              await widget.onPressed();
            },
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: widget.isRecording
              ? Colors.red.withOpacity(0.5)
              : Color(0x6939D2C0),
          borderRadius: BorderRadius.circular(40),
          border: Border.all(
            color: FlutterFlowTheme.of(context).secondaryColor,
            width: 1,
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            _showAnimation
                ? CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  )
                : FaIcon(
                    FontAwesomeIcons.locationDot,
                    color: FlutterFlowTheme.of(context).primaryText,
                    size: 30,
                  ),
          ],
        ),
      ),
    );
  }
}
