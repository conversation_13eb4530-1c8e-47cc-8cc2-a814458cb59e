// lib/components/status_display.dart

import 'package:flutter/material.dart';
import 'package:aslaa/models/device.dart';

class StatusDisplay extends StatelessWidget {
  final String userStatus;
  final Color userStatusColor;
  final Device? device;
  final bool isConnected;

  const StatusDisplay({
    Key? key,
    required this.userStatus,
    required this.userStatusColor,
    this.device,
    required this.isConnected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 5.0),
      child: Column(
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                userStatus,
                style: TextStyle(
                  color: userStatusColor,
                  fontSize: 18, // Updated font size to 18
                  fontWeight: FontWeight.bold,
                  fontFamily: 'PorscheNumber',
                ),
              ),
            ],
          ),
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                device != null
                    ? ((device!.deviceName != ''
                            ? device!.deviceName
                            : device!.deviceNumber) +
                        ' : ${device!.type.toUpperCase()}')
                    : 'Device Is Not Available',
                style: TextStyle(
                  fontSize: 18, // Updated font size to 18
                  color: isConnected ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'PorscheNumber',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
