// lib/components/recording_button.dart

import 'package:flutter/material.dart';
import 'package:aslaa/models/user.dart';

class RecordingButton extends StatelessWidget {
  final bool isRecording;
  final Future<void> Function() onPressed;
  final User? user;

  const RecordingButton({
    Key? key,
    required this.isRecording,
    required this.onPressed,
    required this.user,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Calculate remaining days
    final int daysRemaining =
        ((user?.remainDays ?? 0) / 3600 / 24 / 1000).round();

    // Determine the icon color based on daysRemaining
    Color iconColor =
        Theme.of(context).textTheme.bodyMedium?.color ?? Colors.black;
    if (daysRemaining <= 0) {
      iconColor = Colors.red; // License is over
    } else if (daysRemaining <= 5) {
      iconColor = Colors.yellow; // About to expire
    }

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: IconButton(
              padding: EdgeInsets.zero,
              icon: Icon(
                Icons.calendar_today,
                color: iconColor, // Use the computed color
                size: 18,
              ),
              onPressed: onPressed,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '$daysRemaining D',
              style: TextStyle(
                fontFamily: 'PorscheNumber', // or your digital-7 style
                fontSize: 18,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
