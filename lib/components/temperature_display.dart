import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class TemperatureDisplay extends StatelessWidget {
  final double initialTemp;
  final double targetTemp;

  const TemperatureDisplay({
    Key? key,
    required this.initialTemp,
    required this.targetTemp,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine base color based on brightness
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color defaultColor = isDarkMode ? Colors.white : Colors.black;

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          FaIcon(
            FontAwesomeIcons.fire,
            // If the temperature is negative, use red;
            // otherwise use either white (dark mode) or black (light mode)
            color: (targetTemp < 0) ? Colors.red : defaultColor,
            size: 18,
          ),
          // TweenAnimationBuilder smoothly animates from initialTemp to targetTemp
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: initialTemp, end: targetTemp),
            duration: const Duration(seconds: 3),
            builder: (BuildContext context, double value, Widget? child) {
              return Text(
                '${value.toStringAsFixed(1)}°C',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontFamily: 'porscheNumber',
                      fontSize: 18,
                      // Again, switch between white or black based on brightness
                      color: defaultColor,
                    ),
              );
            },
          ),
        ],
      ),
    );
  }
}
