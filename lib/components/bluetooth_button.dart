// lib/components/bluetooth_button.dart

import 'package:flutter/material.dart';

class BluetoothButton extends StatelessWidget {
  final bool isEnabled;
  final VoidCallback onPressed;
  final String label;
  final IconData enabledIcon;
  final IconData disabledIcon;

  const BluetoothButton({
    Key? key,
    required this.isEnabled,
    required this.onPressed,
    this.label = 'BLE',
    this.enabledIcon = Icons.bluetooth_connected,
    this.disabledIcon = Icons.bluetooth_disabled,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: IconButton(
              padding: EdgeInsets.zero,
              icon: Icon(
                isEnabled ? enabledIcon : disabledIcon,
                color: isEnabled
                    ? Theme.of(context).colorScheme.secondary
                    : Theme.of(context).textTheme.bodyMedium?.color,
                size: 18,
              ),
              onPressed: onPressed,
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'PorscheNumber',
                    fontSize: 18,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
