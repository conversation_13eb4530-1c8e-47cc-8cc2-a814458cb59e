import 'package:aslaa/models/device.dart';
import 'package:flutter/material.dart';

class ChipWidget extends StatefulWidget {
  final DeviceStatus? ds;
  const ChipWidget({Key? key, this.ds}) : super(key: key);

  @override
  _ChipWidgetState createState() => _ChipWidgetState();
}

class _ChipWidgetState extends State<ChipWidget> {
  String color = 'black';
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    DeviceStatus? ds = widget.ds;
    if (ds == null)
      color = 'black';
    else {
      color = ds.online ? 'green' : 'red';
      color = ds.sta == 1 ? 'green' : color;
    }

    return (Stack(
      alignment: AlignmentDirectional(0, 0),
      children: [
        Image.asset(
          'assets/images/chip-$color.png',
          width: 228,
          height: 280,
          fit: BoxFit.cover,
        ),
      ],
    ));
  }
}
