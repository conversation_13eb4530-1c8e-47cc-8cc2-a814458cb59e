// lib/components/weather_display.dart

import 'package:flutter/material.dart';

class WeatherDisplay extends StatelessWidget {
  final String? temperature;

  const WeatherDisplay({
    Key? key,
    required this.temperature,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Parse the temperature to a double
    final double? tempValue = double.tryParse(temperature ?? '');

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            Icons.thermostat,
            // Change color to red if temperature is negative
            color: (tempValue != null && tempValue < 0)
                ? Colors.red
                : Theme.of(context).textTheme.bodyMedium?.color,
            size: 18,
          ),
          const SizedBox(width: 2), // Adjust the spacing as needed
          Flexible(
            child: Text(
              '$temperature°C',
              overflow: TextOverflow.ellipsis, // Prevents text overflow
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'PorscheNumber',
                    fontSize: 18,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
