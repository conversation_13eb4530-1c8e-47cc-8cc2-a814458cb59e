import 'package:aslaa/flutter_flow/flutter_flow_model.dart';

import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';

class OtpVerificationModel extends FlutterFlowModel {
  ///  State fields for stateful widgets in this page.

  // State field(s) for mobile widget.
  TextEditingController? mobileController;
  String? Function(BuildContext, String?)? mobileControllerValidator;
  // State field(s) for PinCode widget.
  TextEditingController? pinCodeController;

  /// Initialization and disposal methods.

  void initState(BuildContext context) {
    pinCodeController = TextEditingController();
  }

  void dispose() {
    mobileController?.dispose();
    pinCodeController?.dispose();
  }

  /// Additional helper methods are added here.
}
