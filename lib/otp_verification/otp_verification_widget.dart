import 'package:aslaa/constant.dart';
import 'package:aslaa/exceptions/http_result_message.dart';
import 'package:aslaa/providers/app_provider.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';

import '../flutter_flow/flutter_flow_animations.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_util.dart';
import '../flutter_flow/flutter_flow_widgets.dart';
import '../flutter_flow/flutter_flow_model.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
// import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'otp_verification_model.dart';
export 'otp_verification_model.dart';

class OtpVerificationWidget extends StatefulWidget {
  const OtpVerificationWidget({
    Key? key,
    this.phoneNumber,
    this.password,
  }) : super(key: key);

  final String? phoneNumber;
  final String? password;

  @override
  _OtpVerificationWidgetState createState() => _OtpVerificationWidgetState();
}

class _OtpVerificationWidgetState extends State<OtpVerificationWidget>
    with TickerProviderStateMixin {
  late OtpVerificationModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final _unfocusNode = FocusNode();

  final animationsMap = {
    'rowOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
        ScaleEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0.4,
          end: 1,
        ),
      ],
    ),
    'textOnPageLoadAnimation1': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 300.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
      ],
    ),
    'columnOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
        MoveEffect(
          curve: Curves.easeInOut,
          delay: 0.ms,
          duration: 600.ms,
          begin: Offset(100, 0),
          end: Offset(0, 0),
        ),
      ],
    ),
    'textOnPageLoadAnimation2': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 300.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
      ],
    ),
    'buttonOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        ScaleEffect(
          curve: Curves.easeInOut,
          delay: 300.ms,
          duration: 600.ms,
          begin: 0,
          end: 1,
        ),
      ],
    ),
  };

  Future<dynamic> _sendOTP(String? phoneNumber) async {
    try {
      final response = await http.post(Uri.parse('$API_HOST/api/auth/login'),
          headers: <String, String>{
            'Content-Type': 'application/json; charset=UTF-8',
          },
          body: jsonEncode({'phoneNumber': phoneNumber}));

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        debugPrint('$json');
        if (json['code'] != null && json['code'] == '200') {
          return true;
        } else {
          String message =
              json['message'] ?? 'Whoops, faield to register, try again';
          debugPrint('$message');
          throw HttpResultException(
              message: message,
              statusCode: 200,
              title: 'Register Error',
              success: false);
        }
      } else {
        throw HttpResultException(
            message:
                'Failed to register through API , ${response.statusCode} error',
            statusCode: response.statusCode,
            title: 'Internal Server Error',
            success: false);
      }
    } catch (err) {
      debugPrint('$err');
      (err is! HttpResultException)
          ? throw HttpResultException(
              message: 'Failed to connect server',
              statusCode: 0,
              title: 'Network Error',
              success: false)
          : throw err;
    }
  }

  Future<dynamic> _verifyOtp(String? phoneNumber, String? otp) async {
    if (phoneNumber == "" || otp == "") return;
    try {
      final response =
          await http.post(Uri.parse('$API_HOST/api/auth/verifyOtp'),
              headers: <String, String>{
                'Content-Type': 'application/json; charset=UTF-8',
              },
              body: jsonEncode({'phoneNumber': phoneNumber, "otp": otp}));

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);
        debugPrint('$json');
        if (json['success'] != null && json['success']) {
          return true;
        } else {
          String message =
              json['err'] ?? 'Whoops, faield to verify otp, try again';
          debugPrint('$message');
          throw HttpResultException(
              message: message,
              statusCode: 200,
              title: 'Register Error',
              success: false);
        }
      } else {
        throw HttpResultException(
            message:
                'Failed to register through API , ${response.statusCode} error',
            statusCode: response.statusCode,
            title: 'Internal Server Error',
            success: false);
      }
    } catch (err) {
      debugPrint('$err');
      (err is! HttpResultException)
          ? throw HttpResultException(
              message: 'Failed to connect server',
              statusCode: 0,
              title: 'Network Error',
              success: false)
          : throw err;
    }
  }

  Future<dynamic> _register(
      String? phoneNumber, String? password, AppProvider provider) async {
    try {
      final response = await http.post(Uri.parse('$API_HOST/api/auth/register'),
          headers: <String, String>{
            'Content-Type': 'application/json; charset=UTF-8',
          },
          body: jsonEncode({'phoneNumber': phoneNumber, 'password': password}));

      if (response.statusCode == 200) {
        Map<String, dynamic> json = jsonDecode(response.body);

        if (json['success'] != null && json['success']) {
          await provider.loginWithToken(json['token']);
          return true;
        } else {
          String message =
              json['message'] ?? 'Whoops, faield to login, try again';
          debugPrint('$message');
          throw HttpResultException(
              message: message,
              statusCode: 200,
              title: 'Register Failed',
              success: false);
        }
      } else {
        throw HttpResultException(
            message:
                'Failed to register through API , ${response.statusCode} error',
            statusCode: response.statusCode,
            title: 'Internal Server Error',
            success: false);
      }
    } catch (err) {
      debugPrint('$err');
      (err is! HttpResultException)
          ? throw HttpResultException(
              message: 'Failed to connect server',
              statusCode: 0,
              title: 'Network Error',
              success: false)
          : throw err;
    }
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => OtpVerificationModel());

    _model.mobileController = TextEditingController();
    debugPrint(widget.phoneNumber);
    if (widget.phoneNumber != null)
      _model.mobileController?.text = widget.phoneNumber!;
  }

  @override
  void dispose() {
    _model.dispose();

    _unfocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AppProvider provider = Provider.of<AppProvider>(context, listen: false);

    return Scaffold(
      key: scaffoldKey,
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(_unfocusNode),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0, 60, 0, 24),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Align(
                                      alignment: AlignmentDirectional(0, 0),
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            0, 24, 0, 24),
                                        child: Text(
                                          FFLocalizations.of(context).getText(
                                            'ybxoljzj' /* OTP VERIFICATION */,
                                          ),
                                          textAlign: TextAlign.center,
                                          maxLines: 2,
                                          style: FlutterFlowTheme.of(context)
                                              .title1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontSize: 22,
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                      ),
                                    ),
                                    Image.asset(
                                      'assets/images/call-image.png',
                                      width: 70,
                                      height: 80,
                                      fit: BoxFit.fitWidth,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ).animateOnPageLoad(
                              animationsMap['rowOnPageLoadAnimation']!),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(24, 0, 24, 0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    FFLocalizations.of(context).getText(
                                      'sfm49190' /* We sent SMS to this number,
Pl... */
                                      ,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .subtitle2
                                        .override(
                                          fontFamily: 'Roboto',
                                          fontSize: 16,
                                          fontWeight: FontWeight.normal,
                                        ),
                                  ).animateOnPageLoad(animationsMap[
                                      'textOnPageLoadAnimation1']!),
                                ],
                              ),
                              Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 20, 0, 0),
                                    child: TextFormField(
                                      controller: _model.mobileController,
                                      obscureText: false,
                                      decoration: InputDecoration(
                                        labelText:
                                            FFLocalizations.of(context).getText(
                                          'tu2keqek' /* Mobile Number */,
                                        ),
                                        hintText:
                                            FFLocalizations.of(context).getText(
                                          'm47ro41n' /* Input your mobile number */,
                                        ),
                                        hintStyle: FlutterFlowTheme.of(context)
                                            .bodyText2,
                                        enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        errorBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        focusedErrorBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                            color: Color(0x00000000),
                                            width: 1,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        filled: true,
                                        fillColor: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyText1
                                          .override(
                                            fontFamily: 'Roboto',
                                            fontWeight: FontWeight.normal,
                                          ),
                                      keyboardType: TextInputType.phone,
                                      validator: _model
                                          .mobileControllerValidator
                                          .asValidator(context),
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp('[0-9]'))
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 12, 0, 0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Text(
                                          FFLocalizations.of(context).getText(
                                            'z4vfh04e' /* Input your otp code */,
                                          ),
                                          textAlign: TextAlign.start,
                                          style: FlutterFlowTheme.of(context)
                                              .subtitle2
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontSize: 16,
                                                fontWeight: FontWeight.normal,
                                              ),
                                        ).animateOnPageLoad(animationsMap[
                                            'textOnPageLoadAnimation2']!),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 12, 0, 0),
                                    child: PinCodeTextField(
                                      appContext: context,
                                      length: 6,
                                      textStyle: FlutterFlowTheme.of(context)
                                          .subtitle2
                                          .override(
                                            fontFamily: 'Roboto',
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryColor,
                                          ),
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      enableActiveFill: false,
                                      autoFocus: true,
                                      showCursor: true,
                                      cursorColor: FlutterFlowTheme.of(context)
                                          .tertiaryColor,
                                      obscureText: false,
                                      hintCharacter: '●',
                                      pinTheme: PinTheme(
                                        fieldHeight: 48,
                                        fieldWidth: 48,
                                        borderWidth: 2,
                                        borderRadius: BorderRadius.circular(12),
                                        shape: PinCodeFieldShape.box,
                                        activeColor:
                                            FlutterFlowTheme.of(context)
                                                .secondaryColor,
                                        inactiveColor:
                                            FlutterFlowTheme.of(context)
                                                .secondaryBackground,
                                        selectedColor:
                                            FlutterFlowTheme.of(context)
                                                .secondaryText,
                                        activeFillColor:
                                            FlutterFlowTheme.of(context)
                                                .secondaryColor,
                                        inactiveFillColor:
                                            FlutterFlowTheme.of(context)
                                                .secondaryBackground,
                                        selectedFillColor:
                                            FlutterFlowTheme.of(context)
                                                .secondaryText,
                                      ),
                                      controller: _model.pinCodeController,
                                      onChanged: (_) => {},
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 24, 0, 24),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  24, 0, 0, 0),
                                          child: FFButtonWidget(
                                            onPressed: () async {
                                              try {
                                                bool? success = await _sendOTP(
                                                    _model.mobileController
                                                        ?.text);
                                                if (success != null &&
                                                    success) {
                                                  showAnimatedSnackbar(
                                                      context,
                                                      "Please check your sms",
                                                      "Otp Sent",
                                                      ContentType.success);
                                                }
                                              } catch (err) {
                                                if (err
                                                    is HttpResultException) {
                                                  showAnimatedSnackbar(
                                                      context,
                                                      err.message,
                                                      err.title,
                                                      ContentType.failure);
                                                }
                                              }
                                            },
                                            text: FFLocalizations.of(context)
                                                .getText(
                                              'c686zvpr' /* Resend OTP */,
                                            ),
                                            options: FFButtonOptions(
                                              width: 130,
                                              height: 40,
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .tertiaryColor,
                                              textStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .subtitle2
                                                      .override(
                                                        fontFamily: 'Roboto',
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                              borderSide: BorderSide(
                                                color: Colors.transparent,
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0, 0, 24, 0),
                                          child: FFButtonWidget(
                                            onPressed: () async {
                                              try {
                                                bool? success =
                                                    await _verifyOtp(
                                                        _model.mobileController
                                                            ?.text,
                                                        _model.pinCodeController
                                                            ?.text);
                                                if (success != null &&
                                                    success) {
                                                  success = await _register(
                                                      _model.mobileController
                                                          ?.text,
                                                      widget.password,
                                                      provider);
                                                  if (success != null &&
                                                      success) {
                                                    context
                                                        .pushNamed('dashboard');
                                                  }
                                                }
                                              } catch (err) {
                                                if (err
                                                    is HttpResultException) {
                                                  showAnimatedSnackbar(
                                                      context,
                                                      err.message,
                                                      err.title,
                                                      ContentType.failure);
                                                }
                                              }
                                            },
                                            text: FFLocalizations.of(context)
                                                .getText(
                                              'exjvbjww' /* Verify OTP */,
                                            ),
                                            options: FFButtonOptions(
                                              width: 130,
                                              height: 40,
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryColor,
                                              textStyle:
                                                  FlutterFlowTheme.of(context)
                                                      .subtitle2
                                                      .override(
                                                        fontFamily: 'Roboto',
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                              borderSide: BorderSide(
                                                color: Colors.transparent,
                                                width: 1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    width:
                                        MediaQuery.of(context).size.width * 0.8,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryBackground,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          FFLocalizations.of(context).getText(
                                            'zt0vnzem' /* Already registered? */,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyText1
                                              .override(
                                                fontFamily: 'Roboto',
                                                fontWeight: FontWeight.normal,
                                              ),
                                        ),
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  24, 0, 4, 0),
                                          child: InkWell(
                                            onTap: () async {
                                              // onGoRegister

                                              context.pushNamed('login');
                                            },
                                            child: Text(
                                              FFLocalizations.of(context)
                                                  .getText(
                                                'rktolxyh' /* Login */,
                                              ),
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyText1
                                                  .override(
                                                    fontFamily: 'Roboto',
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .secondaryColor,
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        InkWell(
                                          onTap: () async {
                                            // onGoRegister

                                            context.pushNamed('login');
                                          },
                                          child: Icon(
                                            Icons.arrow_forward,
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryColor,
                                            size: 24,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ).animateOnPageLoad(
                                  animationsMap['columnOnPageLoadAnimation']!),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(0, 40, 0, 40),
                          child: FFButtonWidget(
                            onPressed: () {
                              print('Button pressed ...');
                            },
                            text: FFLocalizations.of(context).getText(
                              '0u0vora3' /* Continue as Guest */,
                            ),
                            options: FFButtonOptions(
                              width: 200,
                              height: 40,
                              color: FlutterFlowTheme.of(context)
                                  .secondaryBackground,
                              textStyle: FlutterFlowTheme.of(context)
                                  .subtitle2
                                  .override(
                                    fontFamily: 'Roboto',
                                    color: FlutterFlowTheme.of(context)
                                        .primaryText,
                                    fontWeight: FontWeight.w500,
                                  ),
                              borderSide: BorderSide(
                                color: Colors.transparent,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ).animateOnPageLoad(
                              animationsMap['buttonOnPageLoadAnimation']!),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
