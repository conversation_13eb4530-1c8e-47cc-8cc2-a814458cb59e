// lib/services/bluetooth_service.dart

import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class BluetoothService {
  /// Starts scanning for Bluetooth devices.
  /// [onScanResult] is a callback that handles the scan results.
  void startScan(Function(List<ScanResult>) onScanResult) {
    print('BluetoothService: Starting scan...');
    FlutterBluePlus.startScan(timeout: Duration(seconds: 4));
    FlutterBluePlus.scanResults.listen((results) {
      print('BluetoothService: Scan results received.');
      onScanResult(results);
    });
  }

  /// Stops the ongoing Bluetooth scan.
  void stopScan() {
    print('BluetoothService: Stopping scan...');
    FlutterBluePlus.stopScan();
  }

  /// Checks if a scanned device matches the target device based on [deviceNumber] and [manufacturerData].
  /// Returns `true` if there's a match; otherwise, `false`.
  bool checkDevice(String deviceNumber, Map<int, List<int>> manufacturerData) {
    print('BluetoothService: Checking device...');
    if (manufacturerData.isEmpty) return false;

    // Example logic: Check if any manufacturer data list contains the first character code of deviceNumber
    int targetCode = deviceNumber.codeUnits.first;

    for (var dataList in manufacturerData.values) {
      if (dataList.contains(targetCode)) {
        return true;
      }
    }
    return false;
  }
}
