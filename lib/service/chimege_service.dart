import 'dart:io';
import 'package:http/http.dart' as http;

class ChimegeService {
  final String apiUrl = 'https://api.chimege.com/v1.2/transcribe';
  final String apiToken =
      'eec433715f051e669584ffdfa68f181cb8c7abce5c07d4cd0c04bff3974c266c'; // Your API token

  Future<String> transcribe(File audioFile) async {
    final uri = Uri.parse(apiUrl);
    final request = http.Request('POST', uri)
      ..headers['Content-Type'] = 'application/octet-stream'
      ..headers['punctuate'] = 'true'
      ..headers['token'] = apiToken
      ..bodyBytes = await audioFile.readAsBytes();

    final response = await request.send();
    final responseBody = await response.stream.bytesToString();

    if (response.statusCode == 200) {
      return responseBody;
    } else {
      print('Response status code: ${response.statusCode}');
      print('Response body: $responseBody');
      throw Exception('Failed to transcribe audio');
    }
  }
}
