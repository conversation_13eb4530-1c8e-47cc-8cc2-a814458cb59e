// lib/service/speech_to_text_service.dart
import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart' show rootBundle;
import 'package:googleapis/speech/v1.dart';
import 'package:googleapis_auth/auth_io.dart';

class SpeechToTextService {
  final String serviceAccountPath;

  SpeechToTextService(this.serviceAccountPath);

  Future<ServiceAccountCredentials> loadServiceAccountCredentials() async {
    final serviceAccountJson = await rootBundle.loadString(serviceAccountPath);
    final credentials = ServiceAccountCredentials.fromJson(serviceAccountJson);
    return credentials;
  }

  Future<String> transcribeAudio(String audioFilePath) async {
    final credentials = await loadServiceAccountCredentials();

    final scopes = [SpeechApi.cloudPlatformScope];
    final httpClient = await clientViaServiceAccount(credentials, scopes);

    final speechApi = SpeechApi(httpClient);

    final audioBytes = File(audioFilePath).readAsBytesSync();
    final audioContent = base64Encode(audioBytes);

    final request = RecognizeRequest.fromJson({
      "config": {
        "encoding": "LINEAR16",
        "sampleRateHertz": 16000,
        "languageCode": "mn-MN" // Use Mongolian language code
      },
      "audio": {"content": audioContent}
    });

    final response = await speechApi.speech.recognize(request);
    final transcription =
        response.results?.first.alternatives?.first.transcript;

    httpClient.close();

    return transcription ?? '';
  }
}
