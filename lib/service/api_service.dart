// services/api_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  final String apiHost;

  ApiService(this.apiHost);

  Future<Map<String, dynamic>?> fetchSimStatus(String deviceNumber) async {
    try {
      final url = '$apiHost/api/log/sim-status?deviceNumber=$deviceNumber';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['success']) {
          return responseData['data'];
        }
      }
      return null;
    } catch (error) {
      print('Error fetching SIM status: $error');
      return null;
    }
  }

  Future<bool> checkSimCard(String deviceNumber) async {
    try {
      final url = Uri.parse('$apiHost/api/device/check-sim');
      final headers = {'Content-Type': 'application/json; charset=UTF-8'};
      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode({'deviceNumber': deviceNumber}),
      );

      if (response.statusCode == 200) {
        print("checksim: ${response.body}");
        return true;
      }
      return false;
    } catch (error) {
      print('Error checking SIM card: $error');
      return false;
    }
  }

  Future<Map<String, dynamic>?> fetchWeatherData(String location) async {
    try {
      final response = await http.get(
        Uri.parse(
            'https://api.weatherapi.com/v1/current.json?key=fb2157619a394f40aa180455230212&q=$location'),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        print(
            'Failed to fetch weather data. Status Code: ${response.statusCode}');
        return null;
      }
    } catch (err) {
      print('Error fetching weather data: $err');
      return null;
    }
  }
}
