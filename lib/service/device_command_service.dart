// device_command_service.dart

import 'package:flutter/material.dart';
import 'package:aslaa/mqtt/mqtt_websocket.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/utils/ApiCallLimiter.dart';

class DeviceCommandService {
  final MqttHandler? mqttHandler;
  final ApiCallLimiter apiCallLimiter;
  final BuildContext context;

  DeviceCommandService({
    required this.mqttHandler,
    required this.apiCallLimiter,
    required this.context,
  });

  Future<bool> sendDeviceCommand(User user, String command) async {
    if (mqttHandler != null) {
      if (mqttHandler!.isConnected) {
        try {
          bool mqttResult = await mqttHandler!.sendDeviceCommand(user, command);
          print('MQTT_LOGS:: Command sent: $command, Result: $mqttResult');
          return mqttResult;
        } catch (e) {
          print('MQTT_LOGS:: Error sending via MQTT: $e');
          return false;
        }
      } else {
        print('MQTT_LOGS:: MQTT is not connected.');
        return false;
      }
    } else {
      print('MQTT_LOGS:: MQTT handler is null.');
      return false;
    }
  }
}
