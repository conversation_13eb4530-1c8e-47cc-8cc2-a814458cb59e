import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../constant.dart';

class FirebaseMessagingService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  String? kfcmToken;
  String? userId;

  // 创建FirebaseManagerl 单利，全局只有一个对象
  static FirebaseMessagingService? _single;

  FirebaseMessagingService._() {
    initialize();
  }

  static FirebaseMessagingService instance() {
    return _single??= FirebaseMessagingService._();
  }

  Future<void> initialize() async {
    // Request permission for notification
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    print('User granted permission: ${settings.authorizationStatus}');

    setupFCMToken();

    // Configure callbacks
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Received message: ${message.notification?.title}');
      // Handle notification when app is in foreground
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('Opened app from message: ${message.notification?.title}');
      // Handle notification when app is in background or not running
    });

    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
  }

  // Handle notification when app is in background or not running
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('Received background message: ${message.notification?.title}');
    // Show notification in the system tray
  }

  setupFCMToken() async {
    kfcmToken = await FirebaseMessaging.instance.getToken();
    print("kfcmToken --- $kfcmToken");
    updateFCMToken();
    FirebaseMessaging.instance.onTokenRefresh
        .listen((fcmToken) {
      // TODO: If necessary send token to application server.
      kfcmToken = fcmToken;
      updateFCMToken();
      print("fcmToken --- $fcmToken");
      // Note: This callback is fired at each app startup and whenever a new
      // token is generated.
    })
        .onError((err) {
      // Error getting token.
    });
  }

  Future<void> updateFCMToken() async {
    if (isNotEmpty(userId?.isNotEmpty) && isNotEmpty(kfcmToken?.isNotEmpty)) {
      final url = Uri.parse('$API_HOST/api/auth/savefcmtoken');
      final body = {'userid': userId, 'fcmToken': kfcmToken};
      final response = await http.post(url, body: body);
      if (response.statusCode == 200) {
        debugPrint('FCM token saved successfully');
      } else {
        debugPrint('Failed to save FCM token to database');
        throw Exception('Failed to save FCM token to database');
      }
    }

  }

  /// 判断是否为空
  static bool isEmpty(var val) {
    return (val == null || val == '' || val == 'null') ? true : false;
  }

  // 是否不是空字符串
  static bool isNotEmpty(var val) {
    return !isEmpty(val) ? true : false;
  }
}
