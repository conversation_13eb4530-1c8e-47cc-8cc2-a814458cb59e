// services/voice_command_service.dart
import 'dart:io';
import 'package:aslaa/utils/ApiCallLimiter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_sound/flutter_sound.dart';

class VoiceCommandService {
  final FlutterSoundRecorder _audioRecorder = FlutterSoundRecorder();
  bool _isRecorderInitialized = false;
  String _filePath = '';
  bool isRecording = false;
  final ApiCallLimiter _apiCallLimiter = ApiCallLimiter();

  Future<void> init() async {
    final status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      throw RecordingPermissionException('Microphone permission not granted');
    }
    await _audioRecorder.startRecorder();
    _isRecorderInitialized = true;
    _apiCallLimiter.initialize();
  }

  Future<void> startRecording() async {
    if (!_isRecorderInitialized) return;
    Directory tempDir = await getTemporaryDirectory();
    _filePath = '${tempDir.path}/audio_recording.wav';
    isRecording = true;
    await _audioRecorder.startRecorder(
      toFile: _filePath,
      codec: Codec.pcm16WAV,
    );
    print('Recording started: $_filePath');
  }

  Future<void> stopRecording() async {
    if (!_isRecorderInitialized) return;
    await _audioRecorder.stopRecorder();
    print('Recording stopped: $_filePath');
    File audioFile = File(_filePath);
    print('Audio file size: ${audioFile.lengthSync()}');
    String transcription = await transcribeAudio(_filePath);
    print('Transcription: $transcription');
    handleVoiceCommand(transcription);
  }

  Future<String> transcribeAudio(String filePath) async {
    // Implement your transcription logic here
    return "sample transcription";
  }

  void handleVoiceCommand(String command) {
    // Implement your voice command handling logic here
  }

  void dispose() {
    _audioRecorder.closeRecorder();
  }
}
