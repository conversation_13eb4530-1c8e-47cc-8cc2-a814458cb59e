// recording_service.dart

import 'dart:async';
import 'dart:io';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:aslaa/service/speech_to_text_service.dart';

class RecordingService {
  late FlutterSoundRecorder _audioRecorder;
  late SpeechToTextService _speechToTextService;
  bool _isRecorderInitialized = false;
  String _filePath = '';
  bool isRecording = false;

  RecordingService() {
    _audioRecorder = FlutterSoundRecorder();
    _speechToTextService =
        SpeechToTextService('assets/apikey/aslaaios-3bfbd2c34d88.json');
  }

  Future<void> initRecorder() async {
    final status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      throw RecordingPermissionException('Microphone permission not granted');
    }
    await _audioRecorder.startRecorder();
    _isRecorderInitialized = true;
  }

  Future<void> startRecording() async {
    if (!_isRecorderInitialized) return;
    Directory tempDir = await getTemporaryDirectory();
    _filePath = '${tempDir.path}/audio_recording.wav';
    isRecording = true;
    await _audioRecorder.startRecorder(
      toFile: _filePath,
      codec: Codec.pcm16WAV,
    );
    print('Recording started: $_filePath');
    Future.delayed(Duration(seconds: 3), () async {
      await stopRecording();
    });
  }

  Future<void> stopRecording() async {
    if (!_isRecorderInitialized) return;
    await _audioRecorder.stopRecorder();
    print('Recording stopped: $_filePath');
    isRecording = false;
    File audioFile = File(_filePath);
    print('Audio file size: ${audioFile.lengthSync()}');
    String transcription =
        await _speechToTextService.transcribeAudio(_filePath);
    print('Transcription: $transcription');
    // Return or handle the transcription
  }

  void dispose() {
    _audioRecorder.closeRecorder();
  }
}
