<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>NSLocalNetworkUsageDescription</key>
    <string>Looking for local tcp Bonjour service</string>
    <key>NSBonjourServices</key>
    <array>
    <string>mqtt.tcp</string>
    </array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Aslaa</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>mn</string>
	</array>
	<key>CFBundleName</key>
	<string>aslaa</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>aslaa.com</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>aslaa</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<true/>
	<key>FlutterDeepLinkingEnabled</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>want access to Bluetooth permissions to make it easy for you to connect and use your device</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>want access to Bluetooth permissions to make it easy for you to connect and use your device</string>
	<key>NSBonjourServices</key>
	<array>
		<string>mqtt.tcp</string>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>wants to access your camera to select photos and set profile pictures</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>Looking for local tcp Bonjour service</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>wants to access your Location to get your location information to display nearby businesses</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>want access to Bluetooth permissions to make it easy for you to connect and use your device</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>wants to access your Location to get your location information to display nearby businesses</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>wants to access your Location to get your microphone information to voice recognition, voice chat</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>wants to access your camera to select photos and set profile pictures</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>NSLocationWhenInUseUsageDescription</key>
<string>We use your location to provide better services and map functionality.</string>
</dict>
</plist>
